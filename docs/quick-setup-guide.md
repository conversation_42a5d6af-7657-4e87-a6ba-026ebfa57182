# 🚀 订阅支付快速启动指南

## 📋 5分钟快速启动

### 1️⃣ App Store Connect 配置 (10分钟)

```bash
# 访问 App Store Connect
open https://appstoreconnect.apple.com/

# 创建以下产品：
# 1. com.wenhaofree.bridal.single_basic - ¥1 (消耗型)
# 2. com.wenhaofree.bridal.sub_monthly_44 - ¥28 (自动续费订阅)
# 3. com.wenhaofree.bridal.sub_yearly_600 - ¥128 (自动续费订阅)

# 创建沙盒测试账号
# 用户和访问 → 沙盒测试员 → 添加测试员
```

### 2️⃣ 代码配置 (2分钟)

**修改 Package.swift:**
```swift
// 取消注释这些行
.library(name: "StoreKitClientLive", targets: ["StoreKitClientLive"]),

// 在 BridalApp dependencies 中添加
"StoreKitClientLive",

// 取消注释 target 定义
.target(
  name: "StoreKitClientLive",
  dependencies: [
    "SubscriptionCore",
    "UserStateCore",
    .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
  ],
  swiftSettings: swiftSettings
),
```

**修改 BridalApp.swift:**
```swift
// 取消注释
import StoreKitClientLive

// 在配置中使用真实实现
$0.storeKitClient = LiveStoreKitClient.live
```

**修改 SubscriptionCore.swift:**
```swift
extension StoreKitClient: DependencyKey {
  public static var liveValue: StoreKitClient {
    return LiveStoreKitClient.live  // 使用真实实现
  }
}
```

### 3️⃣ 真机测试 (3分钟)

```bash
# 1. 设备准备
# 设置 → App Store → 退出当前账号 (不要登录沙盒账号)

# 2. 运行应用
# Xcode → 选择真机 → Run

# 3. 测试购买
# 打开应用 → 订阅页面 → 选择计划 → 购买
# 使用沙盒账号登录 → 确认购买

# 4. 验证成功
# 检查功能解锁 + 控制台日志
```

## 🔍 快速验证检查

### ✅ 配置检查清单

- [ ] App Store Connect 产品已创建
- [ ] 沙盒测试账号已创建
- [ ] Package.swift 已修改
- [ ] BridalApp.swift 已修改
- [ ] SubscriptionCore.swift 已修改

### ✅ 测试检查清单

- [ ] 应用在真机上运行
- [ ] 订阅页面显示正确价格
- [ ] 购买流程弹出 App Store 界面
- [ ] 沙盒购买成功
- [ ] 功能正确解锁

## 🚨 常见错误快速修复

### 错误1: 产品加载失败
```
❌ Product ID 'xxx' not found
```
**解决:** 检查 App Store Connect 产品配置和 Bundle ID

### 错误2: 购买失败
```
❌ Purchase failed
```
**解决:** 确保使用沙盒账号，检查网络连接

### 错误3: 编译错误
```
❌ Cannot find 'StoreKitClientLive'
```
**解决:** 检查 Package.swift 配置，确保取消注释

## 📞 快速支持

**控制台关键日志:**
```
🛒 Loading products from App Store...     # 产品加载
✅ Loaded X products from App Store       # 加载成功
💳 Starting purchase for: XXX             # 开始购买
✅ Purchase successful: XXX               # 购买成功
```

**测试账号登录位置:**
- ❌ 不要在 设置 → App Store 中登录
- ✅ 在购买时系统弹窗中登录

**重要提醒:**
- 沙盒环境不会真实扣费
- 测试账号只能在沙盒环境使用
- 生产环境需要 App Store 审核通过

---

更详细的信息请参考 [完整配置指南](./subscription-payment-guide.md)。
