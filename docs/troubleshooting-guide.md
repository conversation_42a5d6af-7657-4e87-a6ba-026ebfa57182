# 🔧 订阅支付故障排除指南

## 🚨 常见问题快速诊断

### 问题分类

1. [产品加载问题](#产品加载问题)
2. [购买流程问题](#购买流程问题)
3. [交易验证问题](#交易验证问题)
4. [恢复购买问题](#恢复购买问题)
5. [编译配置问题](#编译配置问题)

---

## 🛒 产品加载问题

### 症状1: 产品列表为空
```
🛒 Loading products from App Store...
⚠️ No products loaded, using fallback subscription plans
```

**可能原因:**
- App Store Connect 产品未配置
- Bundle ID 不匹配
- 网络连接问题
- 产品未审核通过

**解决步骤:**
1. **检查 App Store Connect**
   ```
   登录 → 我的App → 功能 → App内购买项目
   确认产品状态为 "准备提交" 或 "已批准"
   ```

2. **验证 Bundle ID**
   ```
   Xcode → Target → General → Bundle Identifier
   必须与 App Store Connect 中的完全匹配
   ```

3. **检查产品ID**
   ```swift
   // 确保代码中的产品ID与 App Store Connect 完全匹配
   case singleBasic = "com.wenhaofree.bridal.single_basic"
   case monthlyPro = "com.wenhaofree.bridal.sub_monthly_44"
   case yearlyPro = "com.wenhaofree.bridal.sub_yearly_600"
   ```

### 症状2: 部分产品加载失败
```
⚠️ Product ID 'com.wenhaofree.bridal.xxx' not found in App Store Connect
```

**解决方案:**
1. 逐个检查每个产品ID的拼写
2. 确认产品在当前地区可用
3. 检查产品类型配置正确

---

## 💳 购买流程问题

### 症状1: 点击购买无反应
```
🛒 Purchase button tapped!
❌ No plan selected
```

**解决方案:**
1. 确保选择了订阅计划
2. 检查 UI 状态绑定
3. 验证按钮事件处理

### 症状2: 购买弹窗不出现
```
💳 Starting purchase for: Yearly Pro
❌ Purchase failed with error: xxx
```

**可能原因:**
- 设备未登录 Apple ID
- 网络连接问题
- 沙盒环境配置错误

**解决步骤:**
1. **检查设备设置**
   ```
   设置 → App Store → 确保已退出当前账号
   不要在设置中登录沙盒账号
   ```

2. **网络检查**
   ```
   确保设备连接到互联网
   尝试访问 App Store 应用
   ```

3. **重置测试环境**
   ```
   删除应用 → 重新安装 → 重试购买
   ```

### 症状3: 沙盒账号登录失败
```
"此Apple ID仅在沙盒环境中有效"
```

**解决方案:**
1. 确认使用的是沙盒测试账号
2. 检查账号是否在正确的地区
3. 尝试创建新的沙盒账号

---

## 🔐 交易验证问题

### 症状1: 交易验证失败
```
❌ Transaction verification failed
```

**可能原因:**
- 证书签名问题
- Bundle ID 不匹配
- StoreKit 配置错误

**解决步骤:**
1. **检查签名配置**
   ```
   Xcode → Target → Signing & Capabilities
   确保使用正确的开发者账号和证书
   ```

2. **验证 StoreKit 配置**
   ```
   确保项目中包含 .storekit 配置文件
   检查文件中的产品配置
   ```

3. **重新生成证书**
   ```
   如果问题持续，尝试重新生成开发证书
   ```

---

## 🔄 恢复购买问题

### 症状1: 恢复购买无效果
```
🔄 Restoring purchases...
✅ Purchases restored successfully
但订阅状态未更新
```

**解决方案:**
1. **检查账号一致性**
   ```
   确保使用相同的 Apple ID 进行购买和恢复
   ```

2. **验证订阅状态**
   ```
   检查订阅是否仍然有效
   确认没有取消或过期
   ```

3. **强制刷新**
   ```swift
   // 在恢复后强制检查订阅状态
   await send(.checkSubscriptionStatus)
   ```

---

## ⚙️ 编译配置问题

### 症状1: 找不到 StoreKitClientLive
```
❌ Cannot find 'StoreKitClientLive' in scope
```

**解决方案:**
1. **检查 Package.swift**
   ```swift
   // 确保取消注释
   .library(name: "StoreKitClientLive", targets: ["StoreKitClientLive"]),
   
   // 在 BridalApp dependencies 中添加
   "StoreKitClientLive",
   ```

2. **检查 import 语句**
   ```swift
   import StoreKitClientLive
   ```

3. **清理构建**
   ```bash
   Product → Clean Build Folder
   重新构建项目
   ```

### 症状2: 类型歧义错误
```
❌ Type of expression is ambiguous without more context
```

**解决方案:**
1. 添加明确的类型注解
2. 检查依赖版本兼容性
3. 重新导入相关模块

---

## 🔍 调试工具和技巧

### 控制台日志分析

**正常流程日志:**
```
🛒 Loading products from App Store...
✅ Loaded 3 products from App Store
💳 Starting purchase for: Yearly Pro
💳 Initiating real App Store purchase...
✅ Purchase successful: com.wenhaofree.bridal.sub_yearly_600
```

**异常流程日志:**
```
❌ Failed to load products: Error Domain=...
❌ Purchase failed with error: ...
❌ Transaction verification failed: ...
```

### 测试环境验证

**沙盒环境检查:**
```bash
# 确认当前环境
print("Environment: \(Bundle.main.appStoreReceiptURL?.lastPathComponent)")
# 沙盒环境应该显示 "sandboxReceipt"
```

**产品配置验证:**
```swift
// 打印所有加载的产品
for product in products {
    print("Product: \(product.id) - \(product.displayName) - \(product.displayPrice)")
}
```

### 网络问题诊断

**连接测试:**
```bash
# 测试 App Store 连接
ping buy.itunes.apple.com

# 检查防火墙设置
# 确保允许 App Store 相关域名
```

---

## 📞 获取帮助

### 自助诊断步骤

1. **检查控制台日志** - 查找具体错误信息
2. **验证配置** - 对照检查清单逐项确认
3. **重现问题** - 记录详细的重现步骤
4. **环境信息** - 收集设备、系统版本等信息

### 联系支持时提供的信息

```
设备信息:
- 设备型号: iPhone XX
- 系统版本: iOS XX.X
- Xcode 版本: XX.X

问题描述:
- 具体症状
- 重现步骤
- 错误日志
- 已尝试的解决方案

配置信息:
- Bundle ID
- 产品ID列表
- App Store Connect 状态
```

### 有用的资源链接

- [Apple StoreKit 文档](https://developer.apple.com/storekit/)
- [App Store Connect 帮助](https://help.apple.com/app-store-connect/)
- [沙盒测试指南](https://developer.apple.com/app-store/sandboxing/)

---

## ✅ 预防措施

### 开发阶段
- 定期验证产品配置
- 保持代码和配置同步
- 使用版本控制管理配置文件

### 测试阶段
- 建立完整的测试流程
- 记录测试结果和问题
- 定期更新沙盒测试账号

### 发布阶段
- 提前提交产品审核
- 准备详细的审核说明
- 建立用户反馈渠道

记住：大多数问题都是配置相关的，仔细检查每个配置项通常能解决90%的问题！
