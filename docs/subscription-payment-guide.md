# APP内订阅支付完整指南

## 📋 目录

1. [功能概述](#功能概述)
2. [App Store Connect 配置](#app-store-connect-配置)
3. [代码配置](#代码配置)
4. [真机测试流程](#真机测试流程)
5. [生产环境部署](#生产环境部署)
6. [常见问题解决](#常见问题解决)

## 🎯 功能概述

### 当前支持的订阅产品

| 产品类型 | 产品ID | 价格 | 描述 |
|---------|--------|------|------|
| 单次购买 | `com.wenhaofree.bridal.single_basic` | ¥1 | 1次高清图片生成 |
| 月度订阅 | `com.wenhaofree.bridal.sub_monthly_44` | ¥28 | 每月40次生成 |
| 年度订阅 | `com.wenhaofree.bridal.sub_yearly_600` | ¥128 | 每年600次生成（推荐） |

### 技术架构

- **StoreKit 2**: 使用最新的 StoreKit 2 框架
- **TCA 架构**: 基于 The Composable Architecture
- **安全存储**: Keychain 存储订阅状态
- **交易验证**: 自动验证交易安全性

## 🏪 App Store Connect 配置

### 第一步：创建应用

1. 登录 [App Store Connect](https://appstoreconnect.apple.com/)
2. 进入 "我的 App" → 选择你的应用
3. 确保应用状态为 "准备提交" 或更高

### 第二步：配置内购产品

1. **进入内购配置**
   ```
   应用详情 → 功能 → App 内购买项目 → 创建
   ```

2. **创建单次购买产品**
   ```
   类型: 消耗型产品
   产品ID: com.wenhaofree.bridal.single_basic
   价格: ¥1.00
   显示名称: 单次生成
   描述: 1次高清图片生成
   ```

3. **创建订阅产品**
   
   **月度订阅:**
   ```
   类型: 自动续费订阅
   产品ID: com.wenhaofree.bridal.sub_monthly_44
   价格: ¥28.00
   订阅时长: 1个月
   显示名称: 高级月度订阅
   描述: 每月40次生成，解锁所有功能
   ```

   **年度订阅:**
   ```
   类型: 自动续费订阅
   产品ID: com.wenhaofree.bridal.sub_yearly_600
   价格: ¥128.00
   订阅时长: 1年
   显示名称: 年度订阅
   描述: 每年600次生成，享受最大优惠
   ```

### 第三步：创建沙盒测试账号

1. **创建测试账号**
   ```
   用户和访问 → 沙盒测试员 → 添加测试员
   ```

2. **测试账号要求**
   - 使用不同于开发者账号的邮箱
   - 选择测试地区（中国）
   - 记录账号密码

## ⚙️ 代码配置

### 第一步：启用 StoreKitClientLive

1. **修改 Package.swift**
   ```swift
   // 取消注释以下行
   .library(name: "StoreKitClientLive", targets: ["StoreKitClientLive"]),
   
   // 在 BridalApp dependencies 中添加
   "StoreKitClientLive",
   
   // 取消注释 target 定义
   .target(
     name: "StoreKitClientLive",
     dependencies: [
       "SubscriptionCore",
       "UserStateCore",
       .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
     ],
     swiftSettings: swiftSettings
   ),
   ```

2. **修改 BridalApp.swift**
   ```swift
   // 取消注释
   import StoreKitClientLive
   
   // 在配置中使用真实实现
   $0.storeKitClient = LiveStoreKitClient.live
   ```

### 第二步：更新 SubscriptionCore

```swift
extension StoreKitClient: DependencyKey {
  public static var liveValue: StoreKitClient {
    print("🛒 SubscriptionCore: 使用真实 StoreKit 实现...")
    
    #if DEBUG
    // 开发环境：可以选择使用 mock 或真实实现
    return LiveStoreKitClient.live
    #else
    // 生产环境：始终使用真实实现
    return LiveStoreKitClient.live
    #endif
  }
}
```

### 第三步：验证产品配置

确保 `ProductID` 枚举与 App Store Connect 中的产品ID完全匹配：

```swift
public enum ProductID: String, CaseIterable, Sendable {
  case singleBasic = "com.wenhaofree.bridal.single_basic"
  case monthlyPro = "com.wenhaofree.bridal.sub_monthly_44"
  case yearlyPro = "com.wenhaofree.bridal.sub_yearly_600"
  
  public static var allProductIDs: [String] {
    return allCases.map { $0.rawValue }
  }
}
```

## 📱 真机测试流程

### 准备工作

1. **设备准备**
   ```
   设置 → App Store → 退出当前账号
   ⚠️ 不要在设置中登录沙盒账号！
   ```

2. **Xcode 配置**
   ```
   Target → Signing & Capabilities
   - 确保正确的 Team
   - 确保正确的 Bundle ID
   - 添加 StoreKit Configuration 文件
   ```

### 测试步骤

1. **构建并安装**
   ```bash
   # 选择真机设备
   # 运行 Debug 或 Release 版本
   ```

2. **测试产品加载**
   ```
   1. 打开应用
   2. 进入订阅页面
   3. 检查控制台日志：
      🛒 Loading products from App Store...
      ✅ Loaded 3 products from App Store
   4. 验证价格显示正确
   ```

3. **测试购买流程**
   ```
   1. 选择订阅计划
   2. 点击 "Start Free Trial"
   3. 系统弹出 App Store 支付界面
   4. 首次会提示登录沙盒账号
   5. 输入沙盒测试账号和密码
   6. 确认购买（不会真实扣费）
   7. 验证购买成功提示
   8. 检查功能是否解锁
   ```

4. **测试恢复购买**
   ```
   1. 删除应用
   2. 重新安装
   3. 点击 "Restore Purchases"
   4. 验证订阅状态恢复
   ```

### 调试日志检查

**正常流程日志:**
```
🛒 Loading products from App Store...
✅ Loaded 3 products from App Store
💳 Starting purchase for: Yearly Pro
💳 Product ID: com.wenhaofree.bridal.sub_yearly_600
✅ Product: 年度订阅 - ¥128
💳 Initiating real App Store purchase...
💳 Purchase result received
✅ Purchase successful: com.wenhaofree.bridal.sub_yearly_600
🎉 恭喜！您已成功升级到Pro版本
```

## 🚀 生产环境部署

### 第一步：App Store 审核

1. **提交应用审核**
   - 确保所有内购产品已提交审核
   - 应用和内购产品需要一起审核通过

2. **审核要点**
   - 提供测试账号给苹果审核团队
   - 确保购买流程完整可用
   - 提供清晰的产品描述

### 第二步：发布配置

1. **生产环境配置**
   ```swift
   #if DEBUG
   // 开发环境可以使用 mock 进行快速测试
   return mockStoreKitClient()
   #else
   // 生产环境必须使用真实 StoreKit
   return LiveStoreKitClient.live
   #endif
   ```

2. **监控和日志**
   - 添加购买成功/失败的统计
   - 监控订阅转化率
   - 记录关键错误日志

### 第三步：用户支持

1. **常见用户问题**
   - 购买失败处理
   - 恢复购买指导
   - 退款流程说明

2. **客服工具**
   - 提供订阅状态查询
   - 购买记录验证
   - 问题反馈渠道

## 🔧 常见问题解决

### 产品加载失败

**问题:** `Product ID 'xxx' not found in App Store Connect`

**解决方案:**
1. 检查 App Store Connect 中产品配置
2. 确保产品已提交审核并通过
3. 验证 Bundle ID 匹配
4. 检查产品在当前地区是否可用

### 购买失败

**问题:** `Purchase failed with error: xxx`

**解决方案:**
1. 检查沙盒账号是否正确
2. 确保网络连接正常
3. 验证设备时间设置正确
4. 检查是否已有相同订阅

### 交易验证失败

**问题:** `Transaction verification failed`

**解决方案:**
1. 检查 Bundle ID 是否匹配
2. 确保使用正确的证书签名
3. 验证 StoreKit Configuration 文件
4. 检查设备系统版本兼容性

### 恢复购买失败

**问题:** 恢复购买没有效果

**解决方案:**
1. 确保使用相同的 Apple ID
2. 检查订阅是否仍然有效
3. 验证网络连接
4. 尝试重新登录 App Store

## 📊 测试检查清单

### 开发阶段
- [ ] 产品配置正确
- [ ] 代码编译无错误
- [ ] Mock 购买流程正常
- [ ] UI 显示正确

### 测试阶段
- [ ] 真机产品加载成功
- [ ] 沙盒购买流程完整
- [ ] 恢复购买功能正常
- [ ] 错误处理适当
- [ ] 用户体验流畅

### 发布阶段
- [ ] App Store 审核通过
- [ ] 生产环境配置正确
- [ ] 监控系统就绪
- [ ] 用户支持准备完毕

---

## 📞 技术支持

如果遇到问题，请检查：
1. 控制台日志输出
2. App Store Connect 配置
3. 网络连接状态
4. 设备和系统兼容性

更多技术细节请参考 [Apple StoreKit 文档](https://developer.apple.com/storekit/)。
