# 订阅支付调试指南

## 问题诊断

### 1. 检查产品ID配置

确保App Store Connect中配置了以下产品ID：
- `com.bridal.single.basic` (¥1 单次生成)
- `com.wenhaofree.bridal.sub_monthly_44` (¥28 月度高级订阅)
- `com.bridal.yearly` (¥128 年度订阅)

### 2. 沙盒账号设置

1. 在App Store Connect中创建沙盒测试账号
2. 在iOS设备的设置 > App Store > 沙盒账号中登录
3. 确保设备未登录生产环境的Apple ID

### 3. 调试日志

应用会输出详细的调试日志，查看以下关键信息：

```
🎯 Loading subscription products...
🎯 Loaded X products:
  - com.bridal.single.basic: 单次生成 (¥1)
  - com.wenhaofree.bridal.sub_monthly_44: 高级月度订阅 (¥28)
  - com.bridal.yearly: 年度订阅 (¥128)

💳 Starting purchase for: 高级月度订阅
💳 Product ID: com.wenhaofree.bridal.sub_monthly_44
🔍 Looking for product with ID: com.wenhaofree.bridal.sub_monthly_44
🔍 Found 1 products from App Store
✅ Found product: 高级月度订阅 - ¥28.00
💳 Initiating purchase...
```

### 4. 常见问题

#### 问题1: 产品未找到
```
❌ Product not found in App Store Connect
```
**解决方案**: 
- 检查App Store Connect中产品ID是否正确
- 确保产品状态为"准备提交"或"等待审核"
- 等待几分钟让产品同步到沙盒环境

#### 问题2: 沙盒账号问题
```
❌ Purchase failed with error: Cannot connect to iTunes Store
```
**解决方案**:
- 确保使用沙盒测试账号
- 检查网络连接
- 重新登录沙盒账号

#### 问题3: 支付界面未出现
**可能原因**:
- 产品ID不匹配
- 沙盒账号未正确配置
- 应用Bundle ID与App Store Connect不匹配

### 5. 测试步骤

1. **清理应用数据**: 删除应用重新安装
2. **检查日志**: 查看Xcode控制台输出
3. **验证产品加载**: 确保产品列表正确显示
4. **测试购买流程**: 点击订阅按钮
5. **验证支付界面**: 应该弹出App Store支付确认界面

### 6. 环境差异

- **模拟器**: 会模拟成功购买，不会触发真实支付
- **真实设备**: 需要正确的沙盒配置才能触发支付界面

### 7. 调试命令

在真实设备上运行应用时，可以通过以下方式查看详细日志：
1. 连接设备到Xcode
2. 运行应用
3. 查看Xcode控制台输出
4. 寻找以🎯、💳、✅、❌开头的日志信息
