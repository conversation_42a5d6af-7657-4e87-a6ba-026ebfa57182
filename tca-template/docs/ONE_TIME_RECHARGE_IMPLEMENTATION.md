# 一次性订阅成功后调用充值接口功能实现

## 📋 功能概述

实现了一次性订阅成功后自动调用后端接口增加用户积分的功能。当用户购买一次性产品（如单次生成）时，系统会自动调用充值接口为用户账户添加相应的积分。

## 🔧 实现细节

### 1. 数据模型定义

在 `SubscriptionCore.swift` 中添加了充值响应的数据模型：

```swift
public struct OneTimeRechargeResponse: Codable, Sendable, Equatable {
  public let credits: Int
  public let remainingCredits: Int
  public let purchasedAt: String
  public let productId: String
  public let platform: String
  public let id: String
  public let userId: String
  
  enum CodingKeys: String, CodingKey {
    case credits
    case remainingCredits = "remaining_credits"
    case purchasedAt = "purchased_at"
    case productId = "product_id"
    case platform
    case id
    case userId = "user_id"
  }
}
```

### 2. Action 扩展

添加了新的 Action 来处理一次性充值：

```swift
case oneTimeRechargeRequested(String, Int) // userId, credits
case oneTimeRechargeCompleted(OneTimeRechargeResponse)
case oneTimeRechargeFailed(String)
```

### 3. 核心逻辑实现

#### 购买完成处理
修改了 `purchaseCompleted` action 的处理逻辑：

```swift
case let .purchaseCompleted(subscription):
  // ... 现有逻辑 ...
  
  // For one-time purchases, call recharge API to add credits
  if subscription.productID == ProductID.singleBasic.rawValue {
    // Get user ID from stored user session
    if let (user, _) = UserPersistenceService.restoreUserSession() {
      let credits = getCreditsForProduct(subscription.productID)
      await send(.oneTimeRechargeRequested(user.id, credits))
    } else {
      print("⚠️ 无法获取用户ID，跳过充值接口调用")
    }
  }
  
  // ... 其余逻辑 ...
```

#### 充值请求处理
```swift
case let .oneTimeRechargeRequested(userId, credits):
  return .run { send in
    do {
      let response = try await performOneTimeRecharge(
        userId: userId,
        credits: credits,
        networkClient: networkClient
      )
      await send(.oneTimeRechargeCompleted(response))
    } catch {
      await send(.oneTimeRechargeFailed(error.localizedDescription))
    }
  }
```

### 4. 网络请求实现

#### API 调用函数
```swift
private func performOneTimeRecharge(
  userId: String,
  credits: Int,
  networkClient: NetworkClient
) async throws -> OneTimeRechargeResponse {
  // 获取认证token
  guard let authToken = getAuthToken() else {
    throw NetworkError.authenticationRequired("No valid authentication token found")
  }
  
  // 构建请求
  let requestBuilder = RequestBuilder(baseURL: baseURL)
    .path("/api/v1/subscriptions/one-time-recharge")
    .method(.POST)
    .query("user_id", userId)
    .query("images_per_month", String(credits))
    .header("Authorization", authToken)
    .header("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
    .header("Accept", "*/*")
    .header("Host", "127.0.0.1:8000")
    .header("Connection", "keep-alive")
  
  let request = try requestBuilder.build()
  let data = try await networkClient.request(request)
  
  // 解析响应
  let decoder = JSONDecoder()
  return try decoder.decode(OneTimeRechargeResponse.self, from: data)
}
```

#### 产品积分映射
```swift
private func getCreditsForProduct(_ productID: String) -> Int {
  switch productID {
  case ProductID.singleBasic.rawValue:
    return 1
  case ProductID.monthlyPro.rawValue:
    return 40
  case ProductID.yearlyPro.rawValue:
    return 600
  default:
    return 1
  }
}
```

### 5. 认证处理

使用 `AccessTokenManager` 获取认证token：

```swift
private func getAuthToken() -> String? {
  // Try to get token from AccessTokenManager first
  if let authHeader = AccessTokenManager.getAuthorizationHeader() {
    return authHeader
  }
  
  print("⚠️ No authentication token available from AccessTokenManager")
  return nil
}
```

## 🔄 工作流程

1. **用户购买**: 用户购买一次性产品（如单次生成）
2. **购买成功**: StoreKit 返回购买成功结果
3. **触发充值**: 系统检测到是一次性购买产品，触发充值流程
4. **获取用户ID**: 从本地存储获取当前用户ID
5. **调用接口**: 使用用户ID和积分数量调用充值接口
6. **处理响应**: 解析服务器响应，更新用户积分状态
7. **更新UI**: 通过 `addSinglePurchaseCredits` action 更新用户状态

## 📊 API 接口规范

### 请求
- **URL**: `http://127.0.0.1:8000/api/v1/subscriptions/one-time-recharge`
- **方法**: POST
- **参数**: 
  - `user_id`: 用户ID
  - `images_per_month`: 积分数量
- **请求头**:
  - `Authorization`: Bearer token
  - `User-Agent`: Apifox/1.0.0 (https://apifox.com)
  - `Accept`: */*
  - `Host`: 127.0.0.1:8000
  - `Connection`: keep-alive

### 响应
```json
{
  "credits": 5,
  "remaining_credits": 5,
  "purchased_at": "2025-08-05T06:07:00.402911",
  "product_id": "one_time_recharge_5",
  "platform": "stripe",
  "id": "58f0ca39-7570-4da3-ae3a-7f7c54f0d5c0",
  "user_id": "8dce10af-2904-4dc9-883a-c841030c0362"
}
```

## 🧪 测试

创建了完整的测试文件 `test_one_time_recharge.swift` 来验证功能：

- ✅ 产品积分映射测试
- ✅ 网络请求构建测试
- ✅ 响应解析测试
- ✅ 认证token处理测试

## 🔗 依赖关系

- `NetworkClient`: 网络请求处理
- `AuthenticationClient`: 认证token管理
- `UserStateCore`: 用户状态管理
- `UserPersistenceService`: 用户数据持久化

## 🎯 使用场景

此功能主要用于：
1. **单次购买**: 用户购买单次生成积分
2. **订阅激活**: 用户订阅后获得相应积分
3. **积分充值**: 通过购买为账户充值积分

## ⚠️ 注意事项

1. **认证要求**: 必须有有效的认证token才能调用充值接口
2. **用户ID获取**: 需要从本地存储获取用户ID
3. **错误处理**: 完整的错误处理机制，包括网络错误、认证失败等
4. **产品类型**: 目前只对一次性购买产品调用充值接口

## 🚀 功能特性

- ✅ **自动触发**: 购买成功后自动调用充值接口
- ✅ **灵活配置**: 支持不同产品的积分配置
- ✅ **错误处理**: 完整的错误处理和日志记录
- ✅ **状态同步**: 充值成功后自动更新用户状态
- ✅ **测试覆盖**: 完整的测试用例覆盖

🎉 **一次性订阅充值功能实现完成！**
