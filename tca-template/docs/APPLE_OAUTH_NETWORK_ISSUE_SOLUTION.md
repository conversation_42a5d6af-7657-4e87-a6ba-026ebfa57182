# 🔧 Apple OAuth网络连接问题解决方案

## 🎉 **好消息：请求体修复成功！**

从你的最新日志可以确认，**Apple OAuth请求体已经完全修复**：

```json
{
  "identity_token": "eyJraWQiOi...",
  "user_info": {
    "firstName": "浩",
    "email": "<EMAIL>",
    "lastName": "文"
  },
  "real_email": "<EMAIL>",
  "platform": "ios"
}
```

✅ **请求格式完全正确，与你的curl测试一致！**

---

## ❌ **当前问题：网络连接失败**

### **错误分析**
```
Socket SO_ERROR [61: Connection refused]
Could not connect to the server.
HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
```

这是典型的**网络连接被拒绝**错误，不是请求格式问题。

---

## 🛠️ **解决方案**

### **方案1：启动后端服务器（推荐）**

确保你的后端服务器正在8000端口运行：

```bash
# 检查8000端口是否被占用
lsof -i :8000

# 如果没有服务运行，启动你的后端服务器
# 例如：
python manage.py runserver 127.0.0.1:8000
# 或者
npm start
# 或者其他启动命令
```

### **方案2：使用真实设备测试**

iOS模拟器有时无法正确访问localhost。我已经修改了代码来支持真实设备：

#### **修改IP地址**
1. **获取你的电脑IP地址**：
   ```bash
   ifconfig | grep "inet " | grep -v 127.0.0.1
   ```

2. **修改代码中的IP**：
   在 `tca-template/Sources/CommonUI/Constants.swift` 中：
   ```swift
   #if targetEnvironment(simulator)
   public static let baseURL = "http://127.0.0.1:8000"  // 模拟器
   #else
   public static let baseURL = "http://你的电脑IP:8000"  // 真实设备
   #endif
   ```

3. **使用真实iPhone设备测试**

### **方案3：网络诊断工具**

我已经添加了网络诊断工具，现在重新测试时你会看到详细的网络诊断信息：

```
🔍 [NetworkTest] 开始网络诊断...
   目标URL: http://127.0.0.1:8000
📱 [NetworkTest] 本机IP地址: *************
🔌 [NetworkTest] TCP连接测试: 失败
🌐 [NetworkTest] HTTP请求测试: 失败
💡 [NetworkTest] 建议:
   1. 确保后端服务器正在运行
   2. 检查端口8000是否被占用
   3. 如果使用真实设备，请将IP改为电脑的实际IP地址
   4. 检查防火墙设置
```

---

## 🧪 **测试步骤**

### **步骤1：启动后端服务器**
```bash
# 在终端中启动你的后端服务器
python manage.py runserver 127.0.0.1:8000
```

### **步骤2：重新测试Apple ID登录**
1. **重新构建并运行应用**
2. **进行Apple ID登录**
3. **观察控制台日志**

### **期望看到的成功日志**
```
🔍 [NetworkTest] 开始网络诊断...
✅ [NetworkTest] 网络路径可用
✅ [NetworkTest] TCP连接成功到 127.0.0.1:8000
🔌 [NetworkTest] TCP连接测试: 成功
✅ [NetworkTest] HTTP响应状态码: 200
🌐 [NetworkTest] HTTP请求测试: 成功
🔍 开始执行网络请求...
✅ 网络请求成功，响应数据长度: [字节数]
✅ JSON 解析成功
✅ Apple OAuth API 调用成功
```

---

## 🔍 **故障排除清单**

### **检查后端服务器**
- [ ] 后端服务器是否正在运行？
- [ ] 服务器是否监听8000端口？
- [ ] 服务器是否监听127.0.0.1或0.0.0.0？

### **检查网络配置**
- [ ] 防火墙是否阻止了8000端口？
- [ ] 如果使用真实设备，设备和电脑是否在同一WiFi？
- [ ] IP地址配置是否正确？

### **检查应用配置**
- [ ] APIEndpoints.baseURL是否正确？
- [ ] 是否在模拟器还是真实设备上测试？

---

## 💡 **快速验证方法**

### **方法1：浏览器测试**
在浏览器中访问：`http://127.0.0.1:8000/api/v1/oauth/apple/login`

- **如果显示404或其他HTTP错误**：服务器运行正常，但路由可能不存在
- **如果显示"无法访问此网站"**：服务器未运行或端口被阻止

### **方法2：curl测试**
```bash
curl -X POST http://127.0.0.1:8000/api/v1/oauth/apple/login \
  -H "Content-Type: application/json" \
  -d '{"test": "connection"}'
```

### **方法3：检查端口**
```bash
# 检查8000端口状态
netstat -an | grep 8000
# 或者
lsof -i :8000
```

---

## 🎯 **下一步**

1. **启动后端服务器**
2. **重新测试Apple ID登录**
3. **观察网络诊断日志**
4. **如果仍有问题，提供完整的网络诊断日志**

现在请求体格式已经完全正确，只需要解决网络连接问题，Apple OAuth API就能正常工作了！

---

**🚀 总结：请求体修复✅，网络连接待解决🔧**