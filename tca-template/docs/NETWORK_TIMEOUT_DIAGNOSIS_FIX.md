# 🔧 网络请求超时问题诊断和修复

## 🎯 **问题分析**

从你的日志分析，发现了以下关键问题：

### **问题1：IP地址配置问题**
```
🔧 [APIEndpoints] 使用真实设备配置: http://***********:8000
```
但之前检测到的实际本机IP是`***********`，存在不匹配。

### **问题2：网络请求超时**
```
Error Code=-1001 "The request timed out."
Connection 1: failed to connect 1:60, reason -1
```
请求在30秒后超时，说明无法连接到指定的IP地址。

### **问题3：Token正常但连接失败**
从日志可以看到：
- ✅ Token正常：`eyJhbGciOiJIUzI1NiIs...`
- ✅ 请求构建正常：Headers、URL都正确
- ❌ 网络连接失败：无法连接到`***********:8000`

## ✅ **修复方案**

### **修复1：动态IP检测**

#### **修复前 - 硬编码IP**
```swift
let url = "http://***********:8000"  // 可能不是实际IP
```

#### **修复后 - 动态IP检测**
```swift
// 动态获取本机IP地址
if let localIP = getLocalIPAddress() {
  let url = "http://\(localIP):8000"
  print("🔧 [APIEndpoints] 使用动态检测IP配置: \(url)")
  return url
} else {
  let url = "http://***********:8000"  // 备用IP
  print("🔧 [APIEndpoints] 使用备用IP配置: \(url)")
  return url
}
```

### **修复2：添加网络诊断工具**

创建了`SimpleNetworkTester`来帮助诊断网络问题：

```swift
public struct SimpleNetworkTester {
  /// 测试多个IP地址的连接
  public static func testMultipleIPs(port: Int = 8000) async -> [(ip: String, success: Bool, message: String)]
  
  /// 完整的网络诊断
  public static func fullNetworkDiagnosis() async
}
```

### **修复3：在ProfileCore中添加网络诊断**

```swift
// 进行网络诊断（仅在DEBUG模式下）
#if DEBUG
print("🔍 ProfileCore: 开始网络诊断...")
await SimpleNetworkTester.fullNetworkDiagnosis()
#endif
```

## 🧪 **测试验证**

### **现在重新测试设置页面，应该看到：**

#### **1. 动态IP检测日志**
```
🔧 [APIEndpoints] 使用动态检测IP配置: http://***********:8000
```

#### **2. 网络诊断日志**
```
🔍 [SimpleNetworkTester] 开始完整网络诊断...
📱 [SimpleNetworkTester] 设备IP地址: ***********
🔍 [SimpleNetworkTester] 开始测试多个IP地址...
🔍 [SimpleNetworkTester] 测试连接到: http://127.0.0.1:8000
❌ [SimpleNetworkTester] 连接失败: ...
🔍 [SimpleNetworkTester] 测试连接到: http://***********:8000
❌ [SimpleNetworkTester] 连接失败: ...
🔍 [SimpleNetworkTester] 测试连接到: http://***********:8000
✅ [SimpleNetworkTester] 连接成功，状态码: 200，耗时: 0.15秒
💡 [SimpleNetworkTester] 推荐使用IP: ***********
```

#### **3. 正常的API请求**
```
🚀 发送请求到: http://***********:8000/api/v1/login/test-token
✅ 网络请求成功
```

## 🛠️ **后端服务器配置建议**

### **确保服务器监听正确的接口**

#### **Python/Django**
```bash
# 监听所有接口
python manage.py runserver 0.0.0.0:8000

# 或者指定IP
python manage.py runserver ***********:8000
```

#### **Node.js/Express**
```javascript
// 监听所有接口
app.listen(8000, '0.0.0.0', () => {
  console.log('Server running on http://0.0.0.0:8000');
});

// 或者指定IP
app.listen(8000, '***********', () => {
  console.log('Server running on http://***********:8000');
});
```

### **检查防火墙设置**
```bash
# macOS - 检查防火墙状态
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate

# 如果需要，允许8000端口
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --add /path/to/your/server
```

## 📋 **故障排除清单**

### **1. 检查IP地址**
- [ ] 确认设备实际IP地址：`ifconfig | grep "inet " | grep -v 127.0.0.1`
- [ ] 确认应用使用的IP地址：查看日志中的`🔧 [APIEndpoints]`

### **2. 检查服务器状态**
- [ ] 服务器是否正在运行？
- [ ] 服务器是否监听8000端口？：`lsof -i :8000`
- [ ] 服务器是否监听正确的IP？

### **3. 检查网络连接**
- [ ] 设备和服务器是否在同一WiFi网络？
- [ ] 防火墙是否阻止了连接？
- [ ] 可以ping通服务器IP吗？：`ping ***********`

### **4. 测试API连接**
```bash
# 使用curl测试API
curl -X POST http://***********:8000/api/v1/login/test-token \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"
```

## 🎯 **预期效果**

修复后，你应该看到：

1. **✅ 正确的IP地址**：应用自动检测并使用正确的本机IP
2. **✅ 网络诊断信息**：显示哪些IP可用，哪些不可用
3. **✅ 成功的API调用**：如果服务器运行正常
4. **❌ 明确的错误信息**：如果服务器未运行，但不会超时

## 🆘 **如果问题仍然存在**

请提供以下信息：
1. **新的日志输出**（包括网络诊断结果）
2. **服务器启动命令和状态**
3. **设备和服务器的IP地址**
4. **curl测试结果**

这样我们就能精确定位问题并提供针对性的解决方案！

---

**🎉 现在重新测试设置页面，应该能看到详细的网络诊断信息，帮助我们找到正确的IP地址和解决连接问题！**