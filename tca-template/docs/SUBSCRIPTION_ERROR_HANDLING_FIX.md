# 订阅错误处理修复总结

## 问题描述

当API返回状态码1002（无有效订阅）时，应用程序错误地将其映射为通用的"服务器错误"，而不是提示用户需要开通会员订阅。

## 根本原因

1. **错误映射不准确**：状态码1002被映射为`insufficientCredits`错误类型
2. **错误提示不合适**：`insufficientCredits`的提示是"积分不足，请充值后再试"，对订阅用户来说不准确
3. **缺少专门的订阅错误类型**：没有专门处理订阅问题的错误类型

## 解决方案

### 1. 添加新的错误类型

在`GenerationError`枚举中添加了`noActiveSubscription`错误类型：

```swift
public enum GenerationError: Error, Equatable, LocalizedError, Sendable {
  // ... 其他错误类型
  case noActiveSubscription  // 新增
  // ... 其他错误类型
}
```

### 2. 更新错误描述

为新错误类型提供了合适的用户提示：

```swift
case .noActiveSubscription:
  return "您还没有有效的订阅\n\n💎 立即开通会员享受无限生图\n🎯 解锁所有高级功能和模板\n\n让我们为您创造更多美丽瞬间！"
```

### 3. 修改错误映射逻辑

将状态码1002正确映射到新的错误类型，并处理包装的错误：

```swift
switch error {
case .apiError(let code, let message):
  switch code {
  case 1001: // 积分不足
    generationError = .insufficientCredits
  case 1002: // 无有效订阅
    generationError = .noActiveSubscription  // 修改
  case 1003: // 月度限制已达
    generationError = .monthlyLimitReached
  }

case .generationFailed(let message):
  // 处理包装的API错误
  if message.contains("API error (1002)") {
    generationError = .noActiveSubscription
  } else if message.contains("API error (1001)") {
    generationError = .insufficientCredits
  } else if message.contains("API error (1003)") {
    generationError = .monthlyLimitReached
  } else {
    generationError = .serverError
  }
}
```

### 4. 更新UI显示逻辑

在`ImageGenerationView`中为新错误类型添加了专门的UI处理：

- **图标**：使用皇冠图标（`crown.fill`）表示会员功能
- **标题**：显示"需要开通会员"
- **按钮**：提供"立即开通会员"和"单次充值"选项

### 5. 添加导航功能

添加了导航到订阅和充值页面的Action：

```swift
// ImageGenerationCore中的Action
public enum Action: Sendable {
  // ... 其他Action
  case showSubscriptionPage
  case showChargePage
}

// HomeFlow中的委托Action
public enum Action: Sendable {
  // ... 其他Action
  case requestSubscriptionPage
  case requestChargePage
}
```

并在UI中连接了按钮点击事件：

```swift
PrimaryButton(title: "👑 立即开通会员") {
  store.send(.showSubscriptionPage)
}

PrimaryButton(title: "💎 单次充值") {
  store.send(.showChargePage)
}
```

### 6. 实现完整的导航链

建立了完整的Action传递链：

1. **UI层**：用户点击按钮 → `ImageGeneration.Action.showSubscriptionPage`
2. **HomeFlow层**：接收并转换 → `HomeFlow.Action.requestSubscriptionPage`
3. **MainTab层**：处理委托Action → `MainTab.Action.showSubscription(nil)`
4. **UI层**：显示订阅页面的sheet

## 修改的文件

1. `tca-template/Sources/ImageGenerationCore/ImageGenerationCore.swift`
   - 添加`noActiveSubscription`错误类型
   - 更新错误描述和属性
   - 修改错误映射逻辑，包括处理包装的错误
   - 添加导航Action（`showSubscriptionPage`, `showChargePage`）

2. `tca-template/Sources/ImageGenerationSwiftUI/ImageGenerationView.swift`
   - 更新UI显示逻辑
   - 添加订阅相关的图标和按钮
   - 连接按钮点击事件到导航Action

3. `tca-template/Sources/MainTabCore/MainTabCore.swift`
   - 在HomeFlow中添加委托Action（`requestSubscriptionPage`, `requestChargePage`）
   - 在HomeFlow的reducer中处理ImageGeneration的导航Action
   - 在MainTabCore的reducer中处理委托Action，显示订阅页面

## 测试验证

- ✅ 代码编译通过
- ✅ 错误类型正确映射
- ✅ UI显示逻辑更新

## 预期效果

当用户遇到1002错误时，现在会看到：

1. **清晰的错误提示**：明确告知需要开通会员
2. **合适的视觉元素**：皇冠图标表示会员功能
3. **明确的行动指引**：提供开通会员和单次充值的选项
4. **友好的用户体验**：鼓励性的文案而非冷冰冰的错误信息

## 后续工作

- [x] 添加导航Action到ImageGenerationCore
- [x] 连接UI按钮到导航Action
- [x] 在HomeFlow中处理导航Action，实现实际的页面跳转
- [x] 在MainTabCore中处理委托Action，显示订阅页面
- [ ] 添加相关的用户行为统计
- [ ] 测试错误处理逻辑是否正确工作