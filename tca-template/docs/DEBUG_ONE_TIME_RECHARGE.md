# 一次性充值功能调试指南

## 🔍 问题诊断

如果点击一次性订阅成功后没有调用 `/one-time-recharge` 接口，请按以下步骤进行调试：

## 📋 调试步骤

### 1. 检查控制台日志

在Xcode控制台中查找以下关键日志：

#### 购买完成时的日志：
```
🔍 Purchase completed debug info:
   Product ID: com.wenhaofree.bridal.single_basic
   Single Basic Product ID: com.wenhaofree.bridal.single_basic
   Is Single Basic: true
```

#### 如果检测到一次性购买：
```
✅ Detected one-time purchase, preparing to call recharge API
📋 User ID: e6823b76-0a4a-467c-aefb-09f3e4e57969
📋 Credits to add: 1
```

#### 如果开始调用充值接口：
```
💳 Requesting one-time recharge for user e6823b76-0a4a-467c-aefb-09f3e4e57969 with 1 credits
🌐 About to call performOneTimeRecharge function
🔄 Calling performOneTimeRecharge...
```

### 2. 可能的问题和解决方案

#### 问题1: 没有看到购买完成日志
**原因**: 购买流程没有成功完成
**解决方案**: 
- 检查StoreKit Configuration文件是否正确配置
- 确认产品ID是否正确
- 检查是否有购买错误日志

#### 问题2: 产品ID不匹配
**日志特征**: 
```
🔍 Purchase completed debug info:
   Product ID: [其他产品ID]
   Single Basic Product ID: com.wenhaofree.bridal.single_basic
   Is Single Basic: false
ℹ️ Not a one-time purchase product, skipping recharge API call
```

**解决方案**: 
- 确认用户选择的是"单次生成"产品
- 检查产品ID配置是否正确

#### 问题3: 用户未登录
**日志特征**: 
```
✅ Detected one-time purchase, preparing to call recharge API
⚠️ 无法获取用户ID，跳过充值接口调用
```

**解决方案**: 
- 确保用户已登录
- 检查 `UserPersistenceService.restoreUserSession()` 是否返回有效用户

#### 问题4: 网络请求失败
**日志特征**: 
```
💳 Requesting one-time recharge for user ... with ... credits
🌐 About to call performOneTimeRecharge function
🔄 Calling performOneTimeRecharge...
❌ One-time recharge failed: [错误信息]
```

**解决方案**: 
- 检查网络连接
- 确认API服务器是否运行在 `http://127.0.0.1:8000`
- 检查认证token是否有效

### 3. 手动测试步骤

#### 步骤1: 确认产品配置
1. 打开应用的订阅页面
2. 确认能看到"单次生成"选项
3. 确认价格显示为"¥1"

#### 步骤2: 确认用户登录状态
1. 检查应用中是否显示用户信息
2. 确认用户已通过Apple登录或其他方式登录

#### 步骤3: 执行购买流程
1. 点击"单次生成"购买按钮
2. 完成StoreKit购买流程
3. 观察控制台日志输出

#### 步骤4: 检查网络请求
1. 如果有网络抓包工具，监控是否有请求发送到充值接口
2. 检查请求参数是否正确

### 4. 常见错误和解决方案

#### 错误1: "No valid authentication token found"
**解决方案**: 
- 确保用户已登录
- 检查 `AccessTokenManager.getAuthorizationHeader()` 是否返回有效token

#### 错误2: "Product still not available after reload"
**解决方案**: 
- 检查StoreKit Configuration文件
- 确认产品ID配置正确
- 在这种情况下，代码会创建mock购买来测试充值功能

#### 错误3: 网络连接错误
**解决方案**: 
- 确认API服务器运行状态
- 检查防火墙设置
- 确认URL配置正确

### 5. 测试用例

#### 测试用例1: 正常流程测试
```bash
cd Tests
swift test_subscription_flow.swift
```

#### 测试用例2: 充值接口测试
```bash
cd Tests  
swift test_one_time_recharge.swift
```

### 6. 调试技巧

#### 技巧1: 启用详细日志
代码中已添加详细的调试日志，确保在Debug模式下运行应用

#### 技巧2: 使用断点
在以下位置设置断点：
- `purchaseCompleted` action处理
- `oneTimeRechargeRequested` action处理
- `performOneTimeRecharge` 函数

#### 技巧3: 模拟测试
如果StoreKit不可用，代码会自动创建mock购买来测试充值功能

### 7. 预期的完整日志流程

正常情况下，您应该看到以下完整的日志序列：

```
🔍 Purchase completed debug info:
   Product ID: com.wenhaofree.bridal.single_basic
   Single Basic Product ID: com.wenhaofree.bridal.single_basic
   Is Single Basic: true
✅ Detected one-time purchase, preparing to call recharge API
📋 User ID: e6823b76-0a4a-467c-aefb-09f3e4e57969
📋 Credits to add: 1
💳 Requesting one-time recharge for user e6823b76-0a4a-467c-aefb-09f3e4e57969 with 1 credits
🌐 About to call performOneTimeRecharge function
🔄 Calling performOneTimeRecharge...
🌐 Making one-time recharge request...
   User ID: e6823b76-0a4a-467c-aefb-09f3e4e57969
   Credits: 1
🔐 认证token: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
🌐 Request URL: http://127.0.0.1:8000/api/v1/subscriptions/one-time-recharge?user_id=e6823b76-0a4a-467c-aefb-09f3e4e57969&images_per_month=1
🌐 Request headers: [请求头信息]
✅ performOneTimeRecharge completed successfully
✅ One-time recharge completed successfully
   Credits: 1
   Remaining Credits: 1
   Product ID: one_time_recharge_1
```

## 🚀 快速检查清单

- [ ] 用户已登录
- [ ] 选择了"单次生成"产品
- [ ] StoreKit Configuration文件配置正确
- [ ] API服务器运行在 http://127.0.0.1:8000
- [ ] 认证token有效
- [ ] 网络连接正常
- [ ] 控制台显示完整的调试日志

如果以上所有项目都正常，但仍然没有调用充值接口，请提供控制台的完整日志输出以便进一步诊断。
