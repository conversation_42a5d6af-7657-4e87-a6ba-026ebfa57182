# 🔧 环境配置调试指南

## 🎯 **问题分析**

你的Constants.swift配置了不同环境的URL：
- **模拟器**: `http://127.0.0.1:8000`
- **真实设备**: `http://***********:8000`

但从日志显示仍在使用`127.0.0.1:8000`，说明可能在模拟器上测试，或者IP地址配置不正确。

## 🔍 **调试步骤**

### **步骤1：确认当前环境**

现在重新测试Apple ID登录时，你应该能看到新的环境信息日志：

```
🔧 [APIEndpoints] 使用模拟器配置: http://127.0.0.1:8000
🔧 构建网络请求...
   环境信息: DEBUG - iOS Simulator
   Base URL: http://127.0.0.1:8000
```

或者（如果是真实设备）：

```
🔧 [APIEndpoints] 使用真实设备配置: http://***********:8000
🔧 构建网络请求...
   环境信息: DEBUG - Real Device
   Base URL: http://***********:8000
   本机IP地址: *************
   建议URL: http://*************:8000
```

### **步骤2：获取你的电脑实际IP地址**

在终端中运行：
```bash
# 方法1：使用ifconfig
ifconfig | grep "inet " | grep -v 127.0.0.1

# 方法2：使用networksetup（macOS）
networksetup -getinfo "Wi-Fi"

# 方法3：简单方法
ipconfig getifaddr en0
```

### **步骤3：更新IP地址配置**

如果你的电脑IP不是`***********`，需要更新Constants.swift中的配置：

```swift
#else
public static let baseURL = "http://你的实际IP:8000"  // 替换为你的电脑IP
#endif
```

## 🛠️ **快速修复方案**

### **方案1：使用动态IP检测（推荐）**

让我修改代码使用动态IP检测：

```swift
public static let baseURL: String = {
  #if DEBUG
  #if targetEnvironment(simulator)
  let url = "http://127.0.0.1:8000"
  print("🔧 [APIEndpoints] 使用模拟器配置: \\(url)")
  return url
  #else
  // 动态获取本机IP
  if let localIP = getLocalIPAddress() {
    let url = "http://\\(localIP):8000"
    print("🔧 [APIEndpoints] 使用动态IP配置: \\(url)")
    return url
  } else {
    let url = "http://***********:8000"  // 备用IP
    print("🔧 [APIEndpoints] 使用备用IP配置: \\(url)")
    return url
  }
  #endif
  #else
  let url = "https://api.bridal.app"
  print("🔧 [APIEndpoints] 使用生产环境配置: \\(url)")
  return url
  #endif
}()
```

### **方案2：统一使用127.0.0.1（简单方案）**

如果你只在模拟器上测试，可以简化配置：

```swift
public static let baseURL = "http://127.0.0.1:8000"  // 统一使用localhost
```

## 🧪 **测试验证**

### **重新测试Apple ID登录**
1. **重新构建并运行应用**
2. **进行Apple ID登录**
3. **观察控制台日志中的环境信息**

### **期望看到的日志**
```
🔧 [APIEndpoints] 使用[环境类型]配置: http://[IP地址]:8000
🔧 构建网络请求...
   环境信息: DEBUG - [iOS Simulator/Real Device]
   Base URL: http://[IP地址]:8000
   Path: /api/v1/oauth/apple/login
   Full URL: http://[IP地址]:8000/api/v1/oauth/apple/login
```

## 📋 **常见问题**

### **Q: 为什么真实设备还是显示127.0.0.1？**
A: 可能原因：
1. 实际在模拟器上测试，不是真实设备
2. 编译缓存问题，需要Clean Build
3. IP地址配置错误

### **Q: 如何确认是否在真实设备上？**
A: 查看日志中的"环境信息"：
- `DEBUG - iOS Simulator` = 模拟器
- `DEBUG - Real Device` = 真实设备

### **Q: ***********不是我的IP怎么办？**
A: 
1. 获取你的实际IP地址
2. 修改Constants.swift中的IP配置
3. 或者使用动态IP检测方案

## 🎯 **下一步**

1. **重新测试并观察环境信息日志**
2. **确认当前使用的环境（模拟器/真实设备）**
3. **如果是真实设备，确认IP地址是否正确**
4. **提供完整的环境信息日志**

这样我们就能准确定位为什么没有使用***********的IP地址了！