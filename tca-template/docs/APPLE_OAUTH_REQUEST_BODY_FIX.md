# 🔧 Apple OAuth请求体修复完成

## 🎯 **问题分析**

从你的日志对比分析，发现了Apple ID登录请求体不完整的问题：

### ❌ **修复前的问题**
```json
// 应用实际发送（不完整）
{
  "platform": "ios",
  "identity_token": "eyJraWQiOi..."
}
```

### ✅ **期望的完整请求体**
```json
// curl测试请求（完整）
{
  "user_info": {
    "firstName": "test",
    "email": "<EMAIL>", 
    "lastName": "haha"
  },
  "real_email": "<EMAIL>",
  "platform": "ios",
  "identity_token": "eyJraWQiOi..."
}
```

---

## 🔧 **修复内容**

### **修复1：改进用户信息检测逻辑**

#### **修复前**
```swift
// 只有当至少有一个姓名字段时才创建 userInfo
if (firstName != nil && !firstName!.isEmpty) || (lastName != nil && !lastName!.isEmpty) {
  userInfo = UserInfo(firstName: firstName, lastName: lastName, email: credential.email)
  // ...首次登录逻辑
} else {
  // ...非首次登录，不创建userInfo
}
```

#### **修复后**
```swift
// 检查是否有姓名信息
let hasNameInfo = (firstName != nil && !firstName!.isEmpty) || (lastName != nil && !lastName!.isEmpty)

// 检查是否有邮箱信息  
let hasEmailInfo = credential.email != nil && !credential.email!.isEmpty

// 如果有任何用户信息（姓名或邮箱），就创建userInfo
if hasNameInfo || hasEmailInfo {
  userInfo = UserInfo(firstName: firstName, lastName: lastName, email: credential.email)
  // ...详细日志
} else {
  // 即使没有用户信息，也创建一个基本的userInfo以确保请求完整性
  userInfo = UserInfo(firstName: nil, lastName: nil, email: nil)
  logger.info(.authentication, "ℹ️ 没有用户信息，创建空的user_info以保持请求格式一致")
}
```

### **修复2：添加Identity Token邮箱解析**

```swift
/// 从Apple Identity Token中解析邮箱
private func extractEmailFromIdentityToken(_ token: String) -> String? {
  // JWT token格式：header.payload.signature
  let components = token.components(separatedBy: ".")
  guard components.count == 3 else { return nil }
  
  let payload = components[1]
  
  // 添加必要的padding
  var paddedPayload = payload
  let remainder = paddedPayload.count % 4
  if remainder > 0 {
    paddedPayload += String(repeating: "=", count: 4 - remainder)
  }
  
  // Base64解码并JSON解析
  guard let data = Data(base64Encoded: paddedPayload) else { return nil }
  
  do {
    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
       let email = json["email"] as? String {
      return email
    }
  } catch {
    print("解析identity token失败: \(error)")
  }
  
  return nil
}
```

### **修复3：增强邮箱获取逻辑**

```swift
// 获取真实邮箱（如果可用）
let realEmail = credential.email
if let email = realEmail {
  logger.info(.authentication, "✅ 检测到真实邮箱: \(email)")
} else {
  logger.info(.authentication, "ℹ️ 未提供真实邮箱")
  logger.info(.authentication, "   这通常发生在非首次登录或用户选择隐藏邮箱时")
  
  // 尝试从identity token中解析邮箱
  if let decodedEmail = extractEmailFromIdentityToken(tokenString) {
    logger.info(.authentication, "✅ 从identity token中解析到邮箱: \(decodedEmail)")
  }
}

// 尝试从token中获取邮箱作为备用
let finalRealEmail = realEmail ?? extractEmailFromIdentityToken(tokenString)
```

### **修复4：添加详细的请求数据日志**

```swift
logger.info(.authentication, "📋 最终请求数据:")
logger.info(.authentication, "   包含用户信息: \(userInfo != nil)")
logger.info(.authentication, "   包含真实邮箱: \(finalRealEmail != nil)")
if let email = finalRealEmail {
  logger.info(.authentication, "   最终邮箱: \(email)")
}
```

---

## 🧪 **测试步骤**

### **步骤1：重新测试Apple ID登录**
1. **重新构建并运行应用**
2. **进行Apple ID登录**
3. **观察控制台日志**

### **步骤2：期望的新日志输出**

#### **首次登录（有用户信息）**
```
✅ 检测到用户信息:
   姓: 浩
   名: 文
   邮箱: <EMAIL>
   这是首次授权登录（包含姓名信息）
✅ 检测到真实邮箱: <EMAIL>
📋 最终请求数据:
   包含用户信息: true
   包含真实邮箱: true
   最终邮箱: <EMAIL>
📋 完整请求体:
{
  "identity_token": "eyJraWQiOi...",
  "platform": "ios",
  "user_info": {
    "firstName": "浩",
    "lastName": "文",
    "email": "<EMAIL>"
  },
  "real_email": "<EMAIL>"
}
```

#### **非首次登录（无用户信息）**
```
ℹ️ 未提供真实邮箱
   这通常发生在非首次登录或用户选择隐藏邮箱时
✅ 从identity token中解析到邮箱: <EMAIL>
ℹ️ 没有用户信息，创建空的user_info以保持请求格式一致
📋 最终请求数据:
   包含用户信息: true
   包含真实邮箱: true
   最终邮箱: <EMAIL>
📋 完整请求体:
{
  "identity_token": "eyJraWQiOi...",
  "platform": "ios", 
  "user_info": {
    "firstName": null,
    "lastName": null,
    "email": null
  },
  "real_email": "<EMAIL>"
}
```

---

## 🎯 **修复效果**

### ✅ **现在的请求体将包含**
1. **identity_token**：Apple提供的JWT token（必需）
2. **platform**：固定为"ios"（必需）
3. **user_info**：用户信息对象（现在总是包含）
   - 首次登录：包含真实的firstName、lastName、email
   - 非首次登录：包含null值但保持结构一致
4. **real_email**：真实邮箱（优先使用credential.email，备用从token解析）

### 🔄 **处理不同场景**
- **首次登录**：完整的用户信息 + 真实邮箱
- **非首次登录**：空的用户信息结构 + 从token解析的邮箱
- **隐藏邮箱**：从identity token中解析Apple私有邮箱

---

## 🆘 **下一步测试**

现在请重新测试Apple ID登录，并提供：

1. **完整的新日志输出**（包括用户信息检测和请求体）
2. **实际发送的请求体JSON**
3. **是否成功包含了user_info和real_email字段**
4. **从identity token解析邮箱是否成功**

这些修复确保了无论是首次登录还是非首次登录，都会发送完整格式的请求体给后端API！

---

**🎉 修复完成！现在Apple OAuth请求应该包含完整的user_info和real_email字段，与你的curl测试请求格式一致。**