#!/usr/bin/env swift

import Foundation

// 测试订阅流程和一次性充值功能

// 模拟用户数据
struct User: Codable {
    let id: String
    let displayName: String
    let email: String
    
    init(id: String, displayName: String, email: String) {
        self.id = id
        self.displayName = displayName
        self.email = email
    }
}

// 模拟UserPersistenceService
struct UserPersistenceService {
    static func restoreUserSession() -> (user: User, token: String)? {
        // 模拟已登录用户
        let user = User(
            id: "e6823b76-0a4a-467c-aefb-09f3e4e57969",
            displayName: "测试用户",
            email: "<EMAIL>"
        )
        let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ5ODg5NTksInN1YiI6IjhkY2UxMGFmLTI5MDQtNGRjOS04ODNhLWM4NDEwMzBjMDM2MiJ9.YKbIlrdhE8Hry5kxlDOmcK8__MtwhGZf7DV9GJd5IK0"
        return (user, token)
    }
}

// 模拟AccessTokenManager
struct AccessTokenManager {
    static func getAuthorizationHeader() -> String? {
        let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ5ODg5NTksInN1YiI6IjhkY2UxMGFmLTI5MDQtNGRjOS04ODNhLWM4NDEwMzBjMDM2MiJ9.YKbIlrdhE8Hry5kxlDOmcK8__MtwhGZf7DV9GJd5IK0"
        return "bearer \(token)"
    }
}

// 模拟订阅计划
struct SubscriptionPlan {
    let id: String
    let productID: String
    let name: String
    let description: String
    let price: String
    let duration: String
    let features: [String]
    let isRecommended: Bool
}

// 模拟用户订阅
struct UserSubscription {
    let id: String
    let planId: String
    let productID: String
    let startDate: Date
    let endDate: Date
    let isActive: Bool
}

// 产品ID枚举
enum ProductID: String {
    case monthlyPro = "com.wenhaofree.bridal.sub_monthly_44"
    case yearlyPro = "com.wenhaofree.bridal.sub_yearly_600"
    case singleBasic = "com.wenhaofree.bridal.single_basic"
}

// 获取产品积分数量
func getCreditsForProduct(_ productID: String) -> Int {
    switch productID {
    case ProductID.singleBasic.rawValue:
        return 1
    case ProductID.monthlyPro.rawValue:
        return 40
    case ProductID.yearlyPro.rawValue:
        return 600
    default:
        return 1
    }
}

// 模拟订阅计划
func createTestSubscriptionPlans() -> [SubscriptionPlan] {
    return [
        SubscriptionPlan(
            id: ProductID.singleBasic.rawValue,
            productID: ProductID.singleBasic.rawValue,
            name: "单次生成",
            description: "1次高清图片生成",
            price: "¥1",
            duration: "one-time",
            features: [
                "1次高清图片生成",
                "体验所有AI风格",
                "高分辨率输出"
            ],
            isRecommended: false
        ),
        SubscriptionPlan(
            id: ProductID.monthlyPro.rawValue,
            productID: ProductID.monthlyPro.rawValue,
            name: "Monthly Pro",
            description: "Perfect for getting started",
            price: "¥28",
            duration: "per month",
            features: [
                "每月40次高清生成",
                "解锁所有AI风格",
                "优先生成队列",
                "高分辨率输出",
                "无广告体验"
            ],
            isRecommended: false
        ),
        SubscriptionPlan(
            id: ProductID.yearlyPro.rawValue,
            productID: ProductID.yearlyPro.rawValue,
            name: "Yearly Pro",
            description: "Best value for committed users",
            price: "¥128",
            duration: "per year",
            features: [
                "每年600次高清生成",
                "包含月度版所有功能",
                "节省33%费用",
                "独家年度会员特权",
                "优先体验新功能",
                "高级模板库"
            ],
            isRecommended: true
        )
    ]
}

// 模拟购买完成处理
func simulatePurchaseCompleted(subscription: UserSubscription) async {
    print("🎉 购买完成处理开始")
    print("   产品ID: \(subscription.productID)")
    print("   计划ID: \(subscription.planId)")
    print("   是否激活: \(subscription.isActive)")
    
    // 检查是否是一次性购买
    if subscription.productID == ProductID.singleBasic.rawValue {
        print("✅ 检测到一次性购买产品，准备调用充值接口")
        
        // 获取用户ID
        if let (user, _) = UserPersistenceService.restoreUserSession() {
            let credits = getCreditsForProduct(subscription.productID)
            print("📋 用户ID: \(user.id)")
            print("📋 积分数量: \(credits)")
            
            // 模拟调用充值接口
            await simulateOneTimeRecharge(userId: user.id, credits: credits)
        } else {
            print("❌ 无法获取用户ID，跳过充值接口调用")
        }
    } else {
        print("ℹ️ 非一次性购买产品，跳过充值接口调用")
    }
}

// 模拟一次性充值接口调用
func simulateOneTimeRecharge(userId: String, credits: Int) async {
    print("\n🌐 开始调用一次性充值接口")
    print("   用户ID: \(userId)")
    print("   积分数量: \(credits)")
    
    // 获取认证token
    guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
        print("❌ 无法获取认证token")
        return
    }
    
    print("🔐 认证token: \(authToken.prefix(50))...")
    
    // 构建请求URL
    let baseURL = "http://127.0.0.1:8000"
    let endpoint = "/api/v1/subscriptions/one-time-recharge"
    let fullURL = "\(baseURL)\(endpoint)?user_id=\(userId)&images_per_month=\(credits)"
    
    print("🌐 请求URL: \(fullURL)")
    print("📤 请求方法: POST")
    print("📋 请求头:")
    print("   Authorization: \(authToken)")
    print("   User-Agent: Apifox/1.0.0 (https://apifox.com)")
    print("   Accept: */*")
    print("   Host: 127.0.0.1:8000")
    print("   Connection: keep-alive")
    
    // 模拟网络延迟
    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
    
    // 模拟成功响应
    print("\n✅ 充值接口调用成功")
    print("📥 响应数据:")
    print("   获得积分: \(credits)")
    print("   剩余积分: \(credits)")
    print("   购买时间: 2025-08-05T06:07:00.402911")
    print("   产品ID: one_time_recharge_\(credits)")
    print("   平台: stripe")
    print("   交易ID: 58f0ca39-7570-4da3-ae3a-7f7c54f0d5c0")
    print("   用户ID: \(userId)")
}

// 测试完整流程
func testCompleteSubscriptionFlow() async {
    print("🚀 开始测试完整订阅流程")
    print(String(repeating: "=", count: 50))
    
    // 1. 创建订阅计划
    let plans = createTestSubscriptionPlans()
    print("📋 可用订阅计划:")
    for plan in plans {
        print("   - \(plan.name) (\(plan.price)) - \(plan.productID)")
    }
    
    // 2. 选择一次性购买计划
    guard let singlePlan = plans.first(where: { $0.productID == ProductID.singleBasic.rawValue }) else {
        print("❌ 未找到一次性购买计划")
        return
    }
    
    print("\n✅ 用户选择了: \(singlePlan.name)")
    
    // 3. 模拟购买成功
    let mockSubscription = UserSubscription(
        id: UUID().uuidString,
        planId: singlePlan.id,
        productID: singlePlan.productID,
        startDate: Date(),
        endDate: Date(),
        isActive: true
    )
    
    print("\n💳 模拟购买成功")
    
    // 4. 处理购买完成
    await simulatePurchaseCompleted(subscription: mockSubscription)
    
    print("\n🎉 测试完成！")
}

// 运行测试
Task {
    await testCompleteSubscriptionFlow()
    exit(0)
}

// 保持脚本运行
RunLoop.main.run()
