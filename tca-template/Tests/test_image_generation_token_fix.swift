#!/usr/bin/env swift

import Foundation

// 模拟AccessTokenManager
struct AccessTokenManager {
    static func getAuthorizationHeader() -> String? {
        // 模拟从Apple登录获取的新token
        let newToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3MDUzNDAsInN1YiI6IjljNTA5OTM4LWU5OTYtNDU1MS04MGQyLWMwNzRjYjQxNDhkNCJ9.gNOIbrBxMtS77OKinjQi_FSmzDxTRfuNB9dh-Z5Meh8"
        return "bearer \(newToken)"
    }
    
    static func printStatus() {
        print("🔍 图片生成认证Token状态检查:")
        
        if let authHeader = getAuthorizationHeader() {
            print("   ✅ Authorization Header: \(authHeader.prefix(50))...")
        } else {
            print("   ❌ Authorization Header: 未找到")
        }
    }
}

// 模拟图片生成请求
func simulateImageGenerationRequest() {
    print("🎨 模拟图片生成请求")
    print(String(repeating: "=", count: 50))
    
    // 1. 检查认证状态
    print("1️⃣ 检查认证Token状态...")
    AccessTokenManager.printStatus()
    print()
    
    // 2. 获取认证头
    guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
        print("❌ 无法获取认证头，生成失败")
        return
    }
    
    print("2️⃣ 构建图片生成请求...")
    
    // 3. 构建请求头
    let headers = [
        "Authorization": authToken,
        "Content-Type": "application/json",
        "User-Agent": "BridalApp/1.0.0",
        "Accept": "*/*",
        "Connection": "keep-alive"
    ]
    
    print("✅ 请求头构建成功:")
    for (key, value) in headers {
        if key == "Authorization" {
            print("   \(key): \(value.prefix(50))...")
        } else {
            print("   \(key): \(value)")
        }
    }
    print()
    
    // 4. 模拟请求参数
    let requestParams = [
        "filesUrl": ["https://img-bridal.wenhaofree.com/uploads/image_1_BE25782A-ACF8-4161-9825-91BE8D69AFB2.jpeg"],
        "prompt": "Keep the same face from reference image, preserve facial identity, elegant traditional wedding dress style, soft lighting, delicate lace details, romantic bouquet, beautiful background, warm atmosphere, premium texture, flowing white veil, classic posing, refined makeup, dreamy glow, natural posture, tranquil mood, high resolution, professional quality, detailed, beautiful",
        "size": "1:1",
        "nVariants": 1,
        "isEnhance": false,
        "uploadCn": false,
        "enableFallback": false,
        "fallbackModel": "FLUX_MAX",
        "callBackUrl": "http://c89de6f7.natappfree.cc/api/v1/image/callback"
    ] as [String : Any]
    
    print("3️⃣ 请求参数:")
    print("   URL: http://127.0.0.1:8000/api/v1/image/generate-image")
    print("   Method: POST")
    print("   Files: \(requestParams["filesUrl"] as? [String] ?? [])")
    print("   Prompt: \((requestParams["prompt"] as? String ?? "").prefix(50))...")
    print("   Size: \(requestParams["size"] ?? "")")
    print("   Variants: \(requestParams["nVariants"] ?? 0)")
    print()
    
    // 5. 对比修复前后的Token
    print("4️⃣ Token对比分析:")
    
    let oldTokens = [
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MjM3MTksInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Z7cW-lSP1PpR75CX_9w5siOcxgAZGgE5vaWbLbtUdpQ",
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MTY4NDIsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.zATgYVH6GXSS8mBEOANXT1WkhI-Magts0pUnWGx6ZSs",
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1NjE4MDAsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.Hegoj48qBr1NSDL7OF5JMphpEAwtkLOdn5m-aMihxTk"
    ]
    let newToken = authToken
    
    print("   🔴 修复前（多个硬编码过期Token）:")
    for (index, token) in oldTokens.enumerated() {
        print("      Token \(index + 1): \(token.prefix(80))...")
    }
    print()
    print("   🟢 修复后（动态获取有效Token）:")
    print("      \(newToken.prefix(80))...")
    print()
    
    // 6. 解析Token信息
    print("5️⃣ Token信息解析:")
    
    func parseJWT(_ token: String) -> [String: Any]? {
        let tokenParts = token.replacingOccurrences(of: "bearer ", with: "").replacingOccurrences(of: "Bearer ", with: "").components(separatedBy: ".")
        guard tokenParts.count == 3 else { return nil }
        
        let payload = tokenParts[1]
        // 添加padding if needed
        var paddedPayload = payload
        let remainder = payload.count % 4
        if remainder > 0 {
            paddedPayload += String(repeating: "=", count: 4 - remainder)
        }
        
        guard let data = Data(base64Encoded: paddedPayload),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return nil
        }
        
        return json
    }
    
    if let oldPayload = parseJWT(oldTokens[0]) {
        print("   🔴 旧Token信息:")
        if let exp = oldPayload["exp"] as? TimeInterval {
            let expDate = Date(timeIntervalSince1970: exp)
            print("      过期时间: \(expDate)")
            print("      是否过期: \(expDate < Date() ? "是" : "否")")
        }
        if let sub = oldPayload["sub"] as? String {
            print("      用户ID: \(sub)")
        }
    }
    print()
    
    if let newPayload = parseJWT(newToken) {
        print("   🟢 新Token信息:")
        if let exp = newPayload["exp"] as? TimeInterval {
            let expDate = Date(timeIntervalSince1970: exp)
            print("      过期时间: \(expDate)")
            print("      是否过期: \(expDate < Date() ? "是" : "否")")
        }
        if let sub = newPayload["sub"] as? String {
            print("      用户ID: \(sub)")
        }
    }
    print()
    
    // 7. 预期结果
    print("6️⃣ 修复效果预期:")
    print("   ✅ 使用Apple登录后返回的有效access_token")
    print("   ✅ 自动从AccessTokenManager获取认证头")
    print("   ✅ 不再出现'User not found'错误")
    print("   ✅ 图片生成请求应该成功")
    print("   ✅ 统一的认证管理机制")
    print()
    
    print("🎉 图片生成Token修复验证完成！")
}

// 测试代码变更对比
func showCodeChanges() {
    print("📝 代码修复对比")
    print(String(repeating: "=", count: 50))
    
    print("🔴 修复前的问题代码:")
    print("""
    // NewImageGenerationClientLive.swift (第18行)
    private let authToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    
    // NewImageGenerationClient.swift (第471行)  
    let authToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    
    // NewImageGenerationClient.swift (第610行)
    let authToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    
    问题：
    ❌ 多个不同的硬编码Token
    ❌ Token用户ID与Apple登录用户不匹配
    ❌ 导致"User not found"错误
    ❌ 维护困难，容易出错
    """)
    print()
    
    print("🟢 修复后的正确代码:")
    print("""
    // 添加AuthenticationClient导入
    import AuthenticationClient
    
    // 动态获取认证Token
    guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
      print("❌ [NewImageGeneration] No valid authentication token found")
      throw NewImageGenerationError.authenticationRequired
    }
    
    print("🔐 [NewImageGeneration] Using auth token: \\(authToken.prefix(50))...")
    
    优势：
    ✅ 动态获取有效Token
    ✅ 使用Apple登录的access_token
    ✅ 统一的认证管理
    ✅ 自动处理Token过期
    ✅ 更好的错误处理
    """)
    print()
}

// 运行测试
print("🧪 图片生成Token修复验证")
print(String(repeating: "=", count: 60))
print()

simulateImageGenerationRequest()
print()
showCodeChanges()
