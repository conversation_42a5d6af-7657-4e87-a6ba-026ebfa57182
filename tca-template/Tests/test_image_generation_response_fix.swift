#!/usr/bin/env swift

import Foundation

// 模拟服务器返回的响应格式
let mockServerResponse = """
{
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "0f2db4ff915b12f8cd38871d3f",
    "paramJson": "{\\"callBackUrl\\":\\"http://c89de6f7.natappfree.cc/api/v1/image/callback\\",\\"enableFallback\\":false,\\"fallbackModel\\":\\"FLUX_MAX\\",\\"fileUrlOrPromptNotEmpty\\":true,\\"filesUrl\\":[\\"https://img-bridal.wenhaofree.com/uploads/image_1_055B042F-F188-46FC-B2ED-1E6B8EA833D7.jpeg\\"],\\"isEnhance\\":false,\\"nVariants\\":1,\\"nVariantsValid\\":true,\\"prompt\\":\\"Keep the same face from reference image, preserve facial identity, elegant traditional wedding dress style, soft lighting, delicate lace details, romantic bouquet, beautiful background, warm atmosphere, premium texture, flowing white veil, classic posing, refined makeup, dreamy glow, natural posture, tranquil mood, high resolution, professional quality, detailed, beautiful\\",\\"size\\":\\"1:1\\",\\"uploadCn\\":false}",
    "completeTime": null,
    "response": null,
    "successFlag": 0,
    "status": "GENERATING",
    "errorCode": null,
    "errorMessage": null,
    "createTime": 1754016708000,
    "progress": "0.00"
  }
}
"""

// 模拟数据结构
struct RecordInfoResponse: Codable {
    let code: Int
    let msg: String
    let data: RecordInfoData?
    
    var success: Bool {
        return code == 200
    }
    
    var message: String {
        return msg
    }
    
    var isSuccess: Bool {
        return success
    }
    
    var error: String? {
        return nil
    }
}

struct RecordInfoData: Codable {
    let code: Int
    let msg: String
    let data: GenerationRecordData?
}

struct GenerationRecordData: Codable {
    let taskId: String
    let paramJson: String
    let completeTime: String?
    let response: String?
    let successFlag: Int
    let status: String
    let errorCode: String?
    let errorMessage: String?
    let createTime: Int64
    let progress: String
    
    var isSuccess: Bool {
        return successFlag == 1
    }
    
    var imageUrls: [String] {
        // 解析response字段中的图片URL
        guard let response = response,
              let data = response.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let urls = json["imageUrls"] as? [String] else {
            return []
        }
        return urls
    }
}

struct NewTaskStatusResponse: Codable {
    let code: Int
    let message: String
    let data: GenerationRecordData?
    
    var isSuccess: Bool {
        return code == 0
    }
}

// 测试响应解析
func testResponseParsing() {
    print("🧪 测试图片生成状态查询响应解析")
    print(String(repeating: "=", count: 50))
    
    // 1. 解析服务器响应
    print("1️⃣ 解析服务器响应...")
    
    guard let responseData = mockServerResponse.data(using: .utf8) else {
        print("❌ 无法转换响应数据")
        return
    }
    
    do {
        let decoder = JSONDecoder()
        let recordInfoResponse = try decoder.decode(RecordInfoResponse.self, from: responseData)
        
        print("✅ RecordInfoResponse 解析成功:")
        print("   Code: \(recordInfoResponse.code)")
        print("   Message: \(recordInfoResponse.msg)")
        print("   Success: \(recordInfoResponse.success)")
        print("   Is Success: \(recordInfoResponse.isSuccess)")
        print()
        
        // 2. 检查嵌套数据
        if let data = recordInfoResponse.data {
            print("2️⃣ 解析嵌套数据...")
            print("✅ RecordInfoData 解析成功:")
            print("   Code: \(data.code)")
            print("   Message: \(data.msg)")
            print()
            
            // 3. 检查生成记录数据
            if let generationData = data.data {
                print("3️⃣ 解析生成记录数据...")
                print("✅ GenerationRecordData 解析成功:")
                print("   Task ID: \(generationData.taskId)")
                print("   Status: \(generationData.status)")
                print("   Success Flag: \(generationData.successFlag)")
                print("   Progress: \(generationData.progress)")
                print("   Is Success: \(generationData.isSuccess)")
                print("   Create Time: \(generationData.createTime)")
                print("   Image URLs Count: \(generationData.imageUrls.count)")
                print()
                
                // 4. 转换为NewTaskStatusResponse
                print("4️⃣ 转换为兼容格式...")
                let statusResponse = NewTaskStatusResponse(
                    code: recordInfoResponse.data?.code ?? 500,
                    message: recordInfoResponse.data?.msg ?? recordInfoResponse.message,
                    data: recordInfoResponse.data?.data
                )
                
                print("✅ NewTaskStatusResponse 转换成功:")
                print("   Code: \(statusResponse.code)")
                print("   Message: \(statusResponse.message)")
                print("   Is Success: \(statusResponse.isSuccess)")
                print("   Has Data: \(statusResponse.data != nil)")
                print()
                
                // 5. 验证修复效果
                print("5️⃣ 验证修复效果...")
                print("✅ 修复前的问题:")
                print("   ❌ 尝试解析为 NewTaskStatusResponse")
                print("   ❌ 期望字段 'message'，但服务器返回 'msg'")
                print("   ❌ 解析失败: 'The data couldn't be read because it is missing.'")
                print()
                
                print("✅ 修复后的解决方案:")
                print("   ✅ 正确解析为 RecordInfoResponse")
                print("   ✅ 处理服务器实际返回的字段格式")
                print("   ✅ 转换为兼容的 NewTaskStatusResponse 格式")
                print("   ✅ 保持现有代码的兼容性")
                print()
                
            } else {
                print("⚠️ 没有生成记录数据")
            }
        } else {
            print("⚠️ 没有嵌套数据")
        }
        
    } catch {
        print("❌ 解析失败: \(error.localizedDescription)")
        return
    }
    
    print("🎉 响应解析修复验证完成！")
}

// 对比修复前后的代码
func showCodeChanges() {
    print("📝 代码修复对比")
    print(String(repeating: "=", count: 50))
    
    print("🔴 修复前的问题代码:")
    print("""
    // 错误的响应解析
    let statusResponse: NewTaskStatusResponse
    do {
      statusResponse = try decoder.decode(NewTaskStatusResponse.self, from: responseData)
    } catch {
      throw NewImageGenerationError.serverError("Failed to parse response: \\(error.localizedDescription)")
    }
    
    问题：
    ❌ 服务器返回字段是 'msg'，但期望 'message'
    ❌ 响应结构不匹配，导致解析失败
    ❌ 错误信息: "The data couldn't be read because it is missing."
    """)
    print()
    
    print("🟢 修复后的正确代码:")
    print("""
    // 正确的响应解析
    let recordInfoResponse: RecordInfoResponse
    do {
      recordInfoResponse = try decoder.decode(RecordInfoResponse.self, from: responseData)
    } catch {
      throw NewImageGenerationError.serverError("Failed to parse response: \\(error.localizedDescription)")
    }
    
    // 转换为兼容格式
    let statusResponse = NewTaskStatusResponse(
      code: recordInfoResponse.data?.code ?? 500,
      message: recordInfoResponse.data?.msg ?? recordInfoResponse.message,
      data: recordInfoResponse.data?.data
    )
    
    优势：
    ✅ 使用正确的响应结构 RecordInfoResponse
    ✅ 处理服务器实际返回的字段格式
    ✅ 转换为兼容的格式保持现有代码工作
    ✅ 更好的错误处理和调试信息
    """)
    print()
}

// 运行测试
print("🧪 图片生成状态查询响应解析修复验证")
print(String(repeating: "=", count: 60))
print()

testResponseParsing()
print()
showCodeChanges()
