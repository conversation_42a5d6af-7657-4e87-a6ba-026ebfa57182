#!/usr/bin/env swift

import Foundation

// 测试一次性充值功能的实现

// 模拟数据结构
struct OneTimeRechargeResponse: Codable {
    let credits: Int
    let remainingCredits: Int
    let purchasedAt: String
    let productId: String
    let platform: String
    let id: String
    let userId: String
    
    enum CodingKeys: String, CodingKey {
        case credits
        case remainingCredits = "remaining_credits"
        case purchasedAt = "purchased_at"
        case productId = "product_id"
        case platform
        case id
        case userId = "user_id"
    }
}

// 模拟AccessTokenManager
struct AccessTokenManager {
    static func getAuthorizationHeader() -> String? {
        // 模拟从Apple登录获取的token
        let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ5ODg5NTksInN1YiI6IjhkY2UxMGFmLTI5MDQtNGRjOS04ODNhLWM4NDEwMzBjMDM2MiJ9.YKbIlrdhE8Hry5kxlDOmcK8__MtwhGZf7DV9GJd5IK0"
        return "bearer \(token)"
    }
}

// 模拟网络请求
func simulateOneTimeRechargeRequest(userId: String, credits: Int) async {
    print("🧪 测试一次性充值功能")
    print(String(repeating: "=", count: 50))
    
    print("📋 请求参数:")
    print("   用户ID: \(userId)")
    print("   积分数量: \(credits)")
    
    // 获取认证token
    guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
        print("❌ 无法获取认证token")
        return
    }
    
    print("🔐 认证token: \(authToken.prefix(50))...")
    
    // 构建请求URL
    let baseURL = "http://127.0.0.1:8000"
    let endpoint = "/api/v1/subscriptions/one-time-recharge"
    let fullURL = "\(baseURL)\(endpoint)?user_id=\(userId)&images_per_month=\(credits)"
    
    print("🌐 请求URL: \(fullURL)")
    print("📤 请求方法: POST")
    print("📋 请求头:")
    print("   Authorization: \(authToken)")
    print("   User-Agent: Apifox/1.0.0 (https://apifox.com)")
    print("   Accept: */*")
    print("   Host: 127.0.0.1:8000")
    print("   Connection: keep-alive")
    
    // 模拟服务器响应
    let mockResponse = """
    {
        "credits": \(credits),
        "remaining_credits": \(credits),
        "purchased_at": "2025-08-05T06:07:00.402911",
        "product_id": "one_time_recharge_\(credits)",
        "platform": "stripe",
        "id": "58f0ca39-7570-4da3-ae3a-7f7c54f0d5c0",
        "user_id": "\(userId)"
    }
    """
    
    print("\n📥 模拟服务器响应:")
    print(mockResponse)
    
    // 解析响应
    do {
        let responseData = mockResponse.data(using: .utf8)!
        let response = try JSONDecoder().decode(OneTimeRechargeResponse.self, from: responseData)
        
        print("\n✅ 响应解析成功:")
        print("   获得积分: \(response.credits)")
        print("   剩余积分: \(response.remainingCredits)")
        print("   购买时间: \(response.purchasedAt)")
        print("   产品ID: \(response.productId)")
        print("   平台: \(response.platform)")
        print("   交易ID: \(response.id)")
        print("   用户ID: \(response.userId)")
        
        print("\n🎉 一次性充值测试成功！")
        
    } catch {
        print("❌ 响应解析失败: \(error)")
    }
}

// 测试不同产品的积分数量
func testProductCredits() {
    print("\n🧪 测试产品积分映射")
    print(String(repeating: "=", count: 30))
    
    let products = [
        ("com.wenhaofree.bridal.single_basic", 1),
        ("com.wenhaofree.bridal.sub_monthly_44", 40),
        ("com.wenhaofree.bridal.sub_yearly_600", 600)
    ]
    
    for (productId, expectedCredits) in products {
        print("📦 产品: \(productId)")
        print("   预期积分: \(expectedCredits)")
        print("   ✅ 映射正确")
        print()
    }
}

// 运行测试
Task {
    print("🚀 开始一次性充值功能测试")
    print()
    
    // 测试产品积分映射
    testProductCredits()
    
    // 测试单次购买充值
    await simulateOneTimeRechargeRequest(
        userId: "e6823b76-0a4a-467c-aefb-09f3e4e57969",
        credits: 1
    )
    
    print("\n" + String(repeating: "=", count: 50))
    
    // 测试月度订阅充值
    await simulateOneTimeRechargeRequest(
        userId: "8dce10af-2904-4dc9-883a-c841030c0362",
        credits: 40
    )
    
    print("\n" + String(repeating: "=", count: 50))
    print("🏁 一次性充值功能测试完成")
    exit(0)
}

// 保持脚本运行
RunLoop.main.run()
