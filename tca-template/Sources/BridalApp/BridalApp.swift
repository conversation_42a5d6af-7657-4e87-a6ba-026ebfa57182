import SwiftUI
import AppCore
import AppSwiftUI
import ComposableArchitecture
import LaunchCore
import AuthenticationClient
import UserStorageClient
import NetworkClient
import NetworkClientLive
import SubscriptionCore
// import StoreKitClientLive
import ImageGenerationClient
import ImageUploadClient
import ImageUploadClientLive
import NewImageGenerationClient
import NewImageGenerationClientLive

@main
struct BridalApp: App {
  var body: some Scene {
    WindowGroup(content: {
      AppView(
        store: Store(initialState: AppFeature.State.launch(Launch.State())) {
          AppFeature.body
        } withDependencies: {
          // Configure live dependencies safely
          print("🔧 配置应用依赖...")
          $0.authenticationClient = .liveValue
          $0.userStorageClient = .liveValue

          // Configure NetworkClient with detailed logging
          print("🌐 配置网络客户端...")

          // Force link NetworkClientLive module
          _ = NetworkClient.liveValue

          $0.networkClient = .liveValue
          print("✅ NetworkClient 配置完成（Live模式）")

          // Test NetworkClient configuration
          print("🔍 测试 NetworkClient 配置...")
          let testClient = $0.networkClient
          print("🔍 NetworkClient 实例类型: \(type(of: testClient))")
          print("🔍 NetworkClient request 函数: \(type(of: testClient.request))")

          // Verify NetworkClient.liveValue is available
          print("🔍 验证 NetworkClient.liveValue 可用性...")
          let directLiveValue = NetworkClient.liveValue
          print("🔍 直接访问 liveValue 成功: \(type(of: directLiveValue))")

          // Configure Image Generation Client for development/testing
          #if DEBUG
          print("🎨 配置图片生成客户端（开发模式）...")
          $0.imageGenerationClient = .mockValue // Use mock for development
          print("✅ 图片生成客户端配置完成（Mock模式）")
          #else
          $0.imageGenerationClient = .liveValue // Use live for production
          #endif

          // Configure Image Upload Client
          print("📤 配置图片上传客户端...")
          $0.imageUploadClient = .liveValue
          print("✅ 图片上传客户端配置完成（Live模式）")

          // Configure New Image Generation Client
          print("🎨 配置新图片生成客户端...")
          $0.newImageGenerationClient = .liveValue
          print("✅ 新图片生成客户端配置完成（Live模式）")

          // Configure StoreKit for real payments
          // 使用真实的 StoreKit 客户端（支持沙盒和生产环境）
          print("🛒 配置StoreKit客户端...")
          $0.storeKitClient = .liveValue // 使用默认实现
          print("✅ StoreKit客户端配置完成")

          // Debug StoreKit Configuration
          if #available(iOS 15.0, macOS 12.0, *) {
            Task {
              print("🔍 开始StoreKit配置调试...")
              await SubscriptionCore.StoreKitDebugHelper.debugStoreKitConfiguration()
            }
          }

          // 检查环境变量
          print("🔍 环境变量检查:")
          print("  - DISABLE_PAYMENTS: \(ProcessInfo.processInfo.environment["DISABLE_PAYMENTS"] ?? "未设置")")

          // 可选：如果需要完全禁用支付（仅用于UI测试），设置环境变量 DISABLE_PAYMENTS=1
          if ProcessInfo.processInfo.environment["DISABLE_PAYMENTS"] == "1" {
            print("⚠️ 支付功能已禁用（DISABLE_PAYMENTS=1）")
            $0.storeKitClient = .testValue
          }
        }
      )
    })
  }
}
