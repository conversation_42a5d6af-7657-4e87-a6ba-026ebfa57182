import Foundation
import ComposableArchitecture
import LoggingClient
import ImageGenerationCore

// MARK: - Image View Feature

@Reducer
public struct ImageView {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var generatedImage: GeneratedImage
    public var isSaving = false
    public var isSharing = false
    public var saveError: String?
    public var shareError: String?
    public var showingSaveSuccess = false
    public var zoomScale: Double = 1.0
    public var showingImageDetails = false
    
    public init(generatedImage: GeneratedImage) {
      self.generatedImage = generatedImage
      print("🖼️ [ImageView] State initialized with image:")
      print("   - ID: \(generatedImage.id)")
      print("   - Template: \(generatedImage.templateName)")
      print("   - Image Data: \(generatedImage.imageData.count) bytes")
      print("   - Image URL: \(generatedImage.imageUrl ?? "nil")")
    }
  }
  
  public enum Action: Sendable {
    case onAppear
    case saveImage
    case shareImage
    case backToTypeSelection
    case zoomChanged(Double)
    case toggleImageDetails
    case saveCompleted
    case saveFailed(String)
    case shareCompleted
    case shareFailed(String)
    case dismissSaveSuccess
    case clearErrors
    case imageDataDownloaded(Data)
  }
  
  @Dependency(\.loggingClient) var logger
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce { state, action in
      switch action {
      case .onAppear:
        logger.info(.ui, "Image view appeared for image: \(state.generatedImage.id)")
        return .none
        
      case .saveImage:
        logger.info(.ui, "Starting image save process")
        state.isSaving = true
        state.saveError = nil

        return .run { [generatedImage = state.generatedImage] send in
          do {
            // 检查是否有图片数据，如果没有则从URL下载
            if generatedImage.imageData.isEmpty, let imageUrl = generatedImage.imageUrl {
              print("🔄 [ImageView] Image data is empty, downloading from URL: \(imageUrl)")
              let imageData = try await downloadImageData(from: imageUrl)
              print("✅ [ImageView] Downloaded image data: \(imageData.count) bytes")

              // 更新图片数据到状态中，供 SwiftUI 使用
              await send(.imageDataDownloaded(imageData))
            } else if !generatedImage.imageData.isEmpty {
              print("✅ [ImageView] Using existing image data: \(generatedImage.imageData.count) bytes")
              await send(.saveCompleted)
            } else {
              print("❌ [ImageView] No image data or URL available")
              throw ImageSaveError.invalidImageData
            }
          } catch {
            await send(.saveFailed(error.localizedDescription))
          }
        }
        
      case .shareImage:
        logger.info(.ui, "Starting image share process")
        state.isSharing = true
        state.shareError = nil

        return .run { [generatedImage = state.generatedImage] send in
          do {
            // 检查是否有图片数据，如果没有则从URL下载
            let imageData: Data
            if generatedImage.imageData.isEmpty, let imageUrl = generatedImage.imageUrl {
              print("🔄 [ImageView] Image data is empty for sharing, downloading from URL: \(imageUrl)")
              imageData = try await downloadImageData(from: imageUrl)
              print("✅ [ImageView] Downloaded image data for sharing: \(imageData.count) bytes")
            } else if !generatedImage.imageData.isEmpty {
              print("✅ [ImageView] Using existing image data for sharing: \(generatedImage.imageData.count) bytes")
              imageData = generatedImage.imageData
            } else {
              print("❌ [ImageView] No image data or URL available for sharing")
              throw ImageShareError.unknown("图片数据无效，无法分享")
            }

            // 分享图片
            try await presentShareSheet(imageData)

            await send(.shareCompleted)
          } catch {
            await send(.shareFailed(error.localizedDescription))
          }
        }
        
      case .backToTypeSelection:
        logger.info(.ui, "Returning to template selection from image view")
        print("🔙 [ImageView] User requested to go back to type selection")
        // 这个 action 会被 HomeFlow 处理，不需要在这里做导航
        return .none
        
      case .zoomChanged(let scale):
        state.zoomScale = scale
        return .none
        
      case .toggleImageDetails:
        state.showingImageDetails.toggle()
        return .none
        
      case .saveCompleted:
        logger.info(.ui, "Image saved successfully")
        state.isSaving = false
        state.showingSaveSuccess = true
        return .none
        
      case .saveFailed(let error):
        logger.error(.ui, "Image save failed: \(error)")
        state.isSaving = false
        state.saveError = error
        return .none
        
      case .shareCompleted:
        logger.info(.ui, "Image shared successfully")
        state.isSharing = false
        return .none
        
      case .shareFailed(let error):
        logger.error(.ui, "Image share failed: \(error)")
        state.isSharing = false
        state.shareError = error
        return .none
        
      case .dismissSaveSuccess:
        state.showingSaveSuccess = false
        return .none
        
      case .clearErrors:
        state.saveError = nil
        state.shareError = nil
        return .none

      case .imageDataDownloaded(let data):
        // 创建新的 GeneratedImage 包含下载的数据
        let updatedImage = GeneratedImage(
          id: state.generatedImage.id,
          templateId: state.generatedImage.templateId,
          templateName: state.generatedImage.templateName,
          imageData: data,
          generatedAt: state.generatedImage.generatedAt,
          processingTime: state.generatedImage.processingTime,
          imageUrl: state.generatedImage.imageUrl,
          thumbnailUrl: state.generatedImage.thumbnailUrl
        )
        state.generatedImage = updatedImage
        // 标记保存完成（实际保存将在 SwiftUI 层处理）
        state.isSaving = false
        state.showingSaveSuccess = true
        return .none
      }
    }
  }
}

// MARK: - Helper Functions

// 保存功能将在 SwiftUI 层使用 PhotosUI 实现

// 权限处理将在 SwiftUI 层使用 PhotosUI 实现

/// 从URL下载图片数据
private func downloadImageData(from urlString: String) async throws -> Data {
  print("🌐 [ImageView] Starting to download image from: \(urlString)")

  guard let url = URL(string: urlString) else {
    print("❌ [ImageView] Invalid URL: \(urlString)")
    throw ImageSaveError.invalidImageData
  }

  do {
    let (data, response) = try await URLSession.shared.data(from: url)

    // 检查HTTP响应
    if let httpResponse = response as? HTTPURLResponse {
      print("🌐 [ImageView] HTTP Status: \(httpResponse.statusCode)")
      guard httpResponse.statusCode == 200 else {
        print("❌ [ImageView] HTTP Error: \(httpResponse.statusCode)")
        throw ImageSaveError.unknown("HTTP Error: \(httpResponse.statusCode)")
      }
    }

    // 检查数据大小
    print("📊 [ImageView] Downloaded data size: \(data.count) bytes")
    guard !data.isEmpty else {
      print("❌ [ImageView] Downloaded data is empty")
      throw ImageSaveError.invalidImageData
    }

    // 基本验证：检查是否为常见图片格式
    let imageHeaders: [[UInt8]] = [
      [0xFF, 0xD8, 0xFF], // JPEG
      [0x89, 0x50, 0x4E, 0x47], // PNG
      [0x47, 0x49, 0x46], // GIF
      [0x52, 0x49, 0x46, 0x46] // WebP
    ]

    let dataBytes = Array(data.prefix(4))
    let isValidImage = imageHeaders.contains { (header: [UInt8]) -> Bool in
      dataBytes.prefix(header.count).elementsEqual(header)
    }

    guard isValidImage else {
      print("❌ [ImageView] Downloaded data is not a valid image format")
      throw ImageSaveError.invalidImageData
    }

    print("✅ [ImageView] Successfully downloaded and validated image data")
    return data

  } catch {
    print("❌ [ImageView] Failed to download image: \(error)")
    if error is ImageSaveError {
      throw error
    } else {
      throw ImageSaveError.unknown("下载图片失败: \(error.localizedDescription)")
    }
  }
}

private func presentShareSheet(_ imageData: Data) async throws {
  // Mock implementation
  // In real app, present UIActivityViewController
  print("Presenting share sheet")
  
  // Simulate potential failure
  if Double.random(in: 0...1) < 0.05 {
    throw ImageShareError.shareSheetFailed
  }
}

// MARK: - Error Types

public enum ImageSaveError: Error, LocalizedError, Sendable {
  case photoLibraryAccessDenied
  case diskSpaceInsufficient
  case invalidImageData
  case saveFailed
  case unsupportedPlatform
  case downloadFailed
  case invalidUrl
  case unknown(String)

  public var errorDescription: String? {
    switch self {
    case .photoLibraryAccessDenied:
      return "无法访问相册，请在设置中允许访问相册权限"
    case .diskSpaceInsufficient:
      return "存储空间不足，无法保存图片"
    case .invalidImageData:
      return "图片数据无效，无法保存"
    case .saveFailed:
      return "保存图片失败，请重试"
    case .unsupportedPlatform:
      return "当前平台不支持保存到相册"
    case .downloadFailed:
      return "下载图片失败，请检查网络连接"
    case .invalidUrl:
      return "图片链接无效"
    case .unknown(let message):
      return "保存图片失败：\(message)"
    }
  }
}

public enum ImageShareError: Error, LocalizedError, Sendable {
  case shareSheetFailed
  case unknown(String)

  public var errorDescription: String? {
    switch self {
    case .shareSheetFailed:
      return "无法打开分享菜单，请重试"
    case .unknown(let message):
      return "分享图片失败：\(message)"
    }
  }
}