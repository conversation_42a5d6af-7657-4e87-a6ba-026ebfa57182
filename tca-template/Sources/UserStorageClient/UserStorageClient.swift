import Dependencies
import DependenciesMacros
import Foundation
import KeychainClient
import UserStateCore
import AuthenticationClient

// MARK: - User Storage Client

@DependencyClient
public struct UserStorageClient: Sendable {
  public var saveUser: @Sendable (_ user: User, _ token: String) async throws -> Void
  public var loadUser: @Sendable () async throws -> (user: User, token: String)?
  public var deleteUser: @Sendable () async throws -> Void
  public var updateLastLoginDate: @Sendable () async throws -> Void
  public var isUserLoggedIn: @Sendable () async -> Bool = { false }
}

// MARK: - Storage Error

public enum UserStorageError: Error, Equatable, LocalizedError {
  case userNotFound
  case tokenNotFound
  case invalidUserData
  case storageError(String)
  
  public var errorDescription: String? {
    switch self {
    case .userNotFound:
      return "User data not found in storage"
    case .tokenNotFound:
      return "Authentication token not found"
    case .invalidUserData:
      return "Invalid user data format"
    case .storageError(let message):
      return "Storage error: \(message)"
    }
  }
}

// MARK: - Stored User Data

public struct StoredUserData: Codable, Equatable, Sendable {
  public let id: String
  public let email: String
  public let displayName: String
  public let avatarURL: String?
  public let createdAt: Date
  public let subscriptionStatusData: Data  // Store as encoded data
  public let authProvider: String
  public let lastLoginDate: Date
  
  public init(from user: User, authProvider: AuthProvider, lastLoginDate: Date = Date()) {
    self.id = user.id
    self.email = user.email
    self.displayName = user.displayName
    self.avatarURL = user.avatarURL
    self.createdAt = user.createdAt
    self.subscriptionStatusData = (try? JSONEncoder().encode(user.subscriptionStatus)) ?? Data()
    self.authProvider = authProvider.rawValue
    self.lastLoginDate = lastLoginDate
  }
  
  public func toUser() -> User {
    User(
      id: id,
      email: email,
      displayName: displayName,
      avatarURL: avatarURL,
      createdAt: createdAt,
      subscriptionStatus: (try? JSONDecoder().decode(SubscriptionStatus.self, from: subscriptionStatusData)) ?? .free
    )
  }
}

// MARK: - Live Implementation

extension UserStorageClient: DependencyKey {
  public static let liveValue = Self(
    saveUser: { user, token in
      @Dependency(\.keychainClient) var keychain
      
      do {
        // Save access token
        try await keychain.saveString(token, forKey: KeychainKeys.accessToken)
        
        // Save user data
        let authProvider = AuthProvider.apple // Default to Apple for now
        let userData = StoredUserData(from: user, authProvider: authProvider)
        try await keychain.save(userData, forKey: KeychainKeys.userID)
        
        print("✅ 用户数据已保存到Keychain")
        print("   用户ID: \(user.id)")
        print("   邮箱: \(user.email)")
        print("   显示名: \(user.displayName)")
        print("   订阅状态: \(user.subscriptionStatus.displayName)")
        
        // 立即验证保存是否成功
        print("🔍 验证Keychain保存结果...")
        let savedToken = try await keychain.loadString(forKey: KeychainKeys.accessToken)
        let savedUserData = try await keychain.load(StoredUserData.self, forKey: KeychainKeys.userID)
        
        if savedToken != nil && savedUserData != nil {
          print("✅ 验证成功：数据已正确保存到Keychain")
          print("   保存的Token: \(savedToken?.prefix(20) ?? "nil")...")
          print("   保存的用户: \(savedUserData?.displayName ?? "nil")")
        } else {
          print("❌ 验证失败：数据未正确保存到Keychain")
          print("   Token存在: \(savedToken != nil)")
          print("   用户数据存在: \(savedUserData != nil)")
        }
        
      } catch {
        print("❌ 保存用户数据失败: \(error.localizedDescription)")
        throw UserStorageError.storageError(error.localizedDescription)
      }
    },
    
    loadUser: {
      @Dependency(\.keychainClient) var keychain

      do {
        // Load access token
        guard let token = try await keychain.loadString(forKey: KeychainKeys.accessToken) else {
          return nil
        }

        // Load user data
        guard let userData = try await keychain.load(StoredUserData.self, forKey: KeychainKeys.userID) else {
          return nil
        }

        // 获取基础用户信息
        let user = userData.toUser()

        print("✅ 从Keychain加载用户数据成功")
        print("   用户ID: \(user.id)")
        print("   邮箱: \(user.email)")
        print("   显示名: \(user.displayName)")
        print("   最后登录: \(userData.lastLoginDate)")
        print("   最终订阅状态: \(user.subscriptionStatus.displayName)")

        return (user: user, token: token)

      } catch {
        print("❌ 加载用户数据失败: \(error.localizedDescription)")
        return nil
      }
    },
    
    deleteUser: {
      @Dependency(\.keychainClient) var keychain
      
      do {
        try await keychain.delete(KeychainKeys.accessToken)
        try await keychain.delete(KeychainKeys.userID)
        
        print("✅ 用户数据已从Keychain删除")
        
      } catch {
        print("❌ 删除用户数据失败: \(error.localizedDescription)")
        throw UserStorageError.storageError(error.localizedDescription)
      }
    },
    
    updateLastLoginDate: {
      @Dependency(\.keychainClient) var keychain
      
      do {
        // Load existing user data
        guard let userData = try await keychain.load(StoredUserData.self, forKey: KeychainKeys.userID) else {
          throw UserStorageError.userNotFound
        }
        
        // Create updated user data with new login date
        let user = userData.toUser()
        let authProvider = AuthProvider(rawValue: userData.authProvider) ?? .email
        let updatedUserData = StoredUserData(
          from: user,
          authProvider: authProvider,
          lastLoginDate: Date()
        )
        
        // Save updated data
        try await keychain.save(updatedUserData, forKey: KeychainKeys.userID)
        
        print("✅ 最后登录时间已更新")
        
      } catch {
        print("❌ 更新最后登录时间失败: \(error.localizedDescription)")
        throw UserStorageError.storageError(error.localizedDescription)
      }
    },
    
    isUserLoggedIn: {
      @Dependency(\.keychainClient) var keychain
      
      do {
        print("🔍 UserStorageClient: 检查用户登录状态...")
        
        // Check for access token
        let token = try await keychain.loadString(forKey: KeychainKeys.accessToken)
        let hasToken = token != nil
        print("   Token存在: \(hasToken)")
        if let token = token {
          print("   Token: \(token.prefix(20))...")
        }
        
        // Check for user data
        let userData = try await keychain.load(StoredUserData.self, forKey: KeychainKeys.userID)
        let hasUserData = userData != nil
        print("   用户数据存在: \(hasUserData)")
        if let userData = userData {
          print("   用户: \(userData.displayName) (\(userData.email))")
          print("   最后登录: \(userData.lastLoginDate)")
        }
        
        let isLoggedIn = hasToken && hasUserData
        print("   最终结果: isLoggedIn = \(isLoggedIn)")
        
        return isLoggedIn
      } catch {
        print("❌ UserStorageClient: 检查登录状态时出错: \(error)")
        return false
      }
    }
  )
}

// MARK: - Test Implementation

extension UserStorageClient: TestDependencyKey {
  public static let testValue = Self(
    saveUser: { _, _ in },
    loadUser: { nil },
    deleteUser: { },
    updateLastLoginDate: { },
    isUserLoggedIn: { false }
  )
}

// MARK: - Dependency Registration

extension DependencyValues {
  public var userStorageClient: UserStorageClient {
    get { self[UserStorageClient.self] }
    set { self[UserStorageClient.self] = newValue }
  }
}

// MARK: - Convenience Extensions

public extension UserStorageClient {
  func saveAppleUser(_ user: User, token: String) async throws {
    try await saveUser(user, token)
  }
  
  func saveEmailUser(_ user: User, token: String) async throws {
    try await saveUser(user, token)
  }
  
  func autoLogin() async throws -> (user: User, token: String)? {
    guard await isUserLoggedIn() else { return nil }
    return try await loadUser()
  }
}
