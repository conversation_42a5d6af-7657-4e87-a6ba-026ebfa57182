import ComposableArchitecture
import CommonUI
import ProfileCore
import UserStateCore
import NotificationSettingsSwiftUI
import NotificationSettingsCore
import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

public struct ProfileView: View {
  @Perception.Bindable var store: StoreOf<ProfileCore.Profile>

  public init(store: StoreOf<ProfileCore.Profile>) {
    self.store = store
  }

  public var body: some View {
    WithPerceptionTracking {
      SettingsView(store: store)
      .navigationTitle("设置")
      .neumorphicNavigationStyle()
      .neumorphicBackground()
      .onAppear {
        store.send(ProfileCore.Profile.Action.onAppear)
      }
      .sheet(isPresented: Binding(
        get: { store.notificationSettings != nil },
        set: { isPresented in
          if !isPresented {
            store.send(ProfileCore.Profile.Action.dismissNotificationSettings)
          }
        }
      )) {
        if let notificationStore = store.scope(state: \.notificationSettings, action: \.notificationSettings) {
          NavigationView {
            NotificationSettingsView(store: notificationStore)
              .toolbar {
                ToolbarItem(placement: {
                  #if os(iOS)
                  return .navigationBarTrailing
                  #else
                  return .automatic
                  #endif
                }()) {
                  Button("完成") {
                    store.send(ProfileCore.Profile.Action.dismissNotificationSettings)
                  }
                }
              }
          }
        }
      }
      .alert(
        "删除账号",
        isPresented: Binding(
          get: { store.isShowingDeleteAccountAlert },
          set: { _ in store.send(ProfileCore.Profile.Action.cancelDeleteAccount) }
        )
      ) {
        Button("取消", role: .cancel) {
          store.send(ProfileCore.Profile.Action.cancelDeleteAccount)
        }

        Button("确认删除", role: .destructive) {
          store.send(ProfileCore.Profile.Action.confirmDeleteAccount)
        }
      } message: {
        Text("删除账号后，您的所有数据将被永久删除且无法恢复。确定要继续吗？")
      }
      .alert(
        "账号删除成功",
        isPresented: Binding(
          get: { store.isShowingDeleteSuccessAlert },
          set: { _ in store.send(ProfileCore.Profile.Action.dismissDeleteSuccessAlert) }
        )
      ) {
        Button("确定") {
          store.send(ProfileCore.Profile.Action.dismissDeleteSuccessAlert)
        }
      } message: {
        Text("您的账号已成功删除，感谢您使用我们的服务。")
      }
      .alert(
        "订阅成功！",
        isPresented: Binding(
          get: { store.isShowingSubscriptionSuccessAlert },
          set: { _ in store.send(ProfileCore.Profile.Action.dismissSubscriptionSuccessAlert) }
        )
      ) {
        Button("太棒了！") {
          store.send(ProfileCore.Profile.Action.dismissSubscriptionSuccessAlert)
        }
      } message: {
        Text("🎉 恭喜您成功升级到Pro版本！\n现在可以享受所有高级功能了。")
      }
      .alert(
        "支付失败",
        isPresented: Binding(
          get: { store.error != nil },
          set: { _ in store.send(ProfileCore.Profile.Action.clearError) }
        )
      ) {
        Button("确定") {
          store.send(ProfileCore.Profile.Action.clearError)
        }
        Button("重试") {
          store.send(ProfileCore.Profile.Action.upgradeSubscription)
        }
      } message: {
        Text(store.error?.localizedDescription ?? "支付过程中发生错误，请重试。")
      }
    }
  }
}

// MARK: - Settings View

struct SettingsView: View {
  @Perception.Bindable var store: StoreOf<ProfileCore.Profile>

  var body: some View {
    WithPerceptionTracking {
      ScrollView {
        LazyVStack(spacing: NeumorphicDesign.largeSpacing) {
          // 用户信息卡片
          userInfoCard

          // 积分卡片
          creditsCard

          // 账户状态卡片
          accountStatusCard

          // Pro订阅推广卡片 (如果是免费用户)
          // if store.user?.subscriptionStatus == .free {
          //   proUpgradeCard
          // }
          
          // 应用设置
          appSettingsSection
          
          // 关于应用
          aboutSection
          
          // 账户操作
          accountActionsSection
          
          // 底部间距
          Spacer(minLength: 100)
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
        .padding(.top, NeumorphicDesign.largePadding)
      }
    }
  }

  // MARK: - 🌸 新拟物风格用户信息卡片
  
  @ViewBuilder
  private var userInfoCard: some View {
    ContentCard(style: .soft) {
      VStack(spacing: NeumorphicDesign.mediumSpacing) {
        // 用户头像和基本信息
        HStack(spacing: NeumorphicDesign.mediumSpacing) {
          // 🌸 新拟物风格头像
          ZStack {
            Circle()
              .fill(
                LinearGradient(
                  colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
              .frame(width: 80, height: 80)
              .shadow(color: .shadowDark.opacity(0.2), radius: 8, x: 4, y: 4)
              .shadow(color: .shadowLight, radius: 8, x: -4, y: -4)
            Circle()
              .fill(
                LinearGradient(
                  colors: [.softPink.opacity(0.3), .warmOrange.opacity(0.3)],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
              .frame(width: 60, height: 60)
            if let user = store.user {
              Text(user.initials)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.textPrimary)
            } else {
              Image(systemName: "person.fill")
                .font(.system(size: 24))
                .foregroundColor(.textSecondary)
            }
          }

          // 用户信息
          VStack(alignment: .leading, spacing: 4) {
            Text(store.displayName)
              .font(.cardTitle)
              .fontWeight(.bold)
              .foregroundColor(.textPrimary)

            // 优先显示API返回的邮箱，否则使用本地用户数据
            if let userProfile = store.userProfile {
              Text(userProfile.email)
                .font(.customBody)
                .foregroundColor(.textSecondary)

              Text("加入于 \(formatAPIDate(userProfile.createdAt))")
                .font(.customCaption)
                .foregroundColor(.textSecondary)

              // 显示剩余积分
              HStack(spacing: 4) {
                Image(systemName: "star.fill")
                  .font(.system(size: 12))
                  .foregroundColor(.warmOrange)
                Text("剩余积分: \(userProfile.remainingCredits)")
                  .font(.customCaption)
                  .fontWeight(.medium)
                  .foregroundColor(.textPrimary)
              }
              .padding(.top, 2)
            } else if let user = store.user {
              Text(user.email)
                .font(.customBody)
                .foregroundColor(.textSecondary)

              Text("加入于 \(formatDate(user.createdAt))")
                .font(.customCaption)
                .foregroundColor(.textSecondary)
            }

            // 显示加载状态
            if store.isLoadingProfile {
              HStack(spacing: 4) {
                ProgressView()
                  .scaleEffect(0.7)
                Text("正在加载详细信息...")
                  .font(.customCaption)
                  .foregroundColor(.textSecondary)
              }
            }
          }
          
          Spacer()
        }
      }
    }
  }

  // MARK: - 🌸 新拟物风格积分卡片

  @ViewBuilder
  private var creditsCard: some View {
    WithPerceptionTracking {
      ContentCard(style: .soft) {
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          // 卡片标题
          HStack {
            HStack(spacing: 8) {
              ZStack {
                Circle()
                  .fill(
                    LinearGradient(
                      colors: [.warmOrange.opacity(0.8), .warmOrange.opacity(0.6)],
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    )
                  )
                  .frame(width: 32, height: 32)
                  .shadow(color: .warmOrange.opacity(0.3), radius: 4, x: 0, y: 2)
                Image(systemName: "star.fill")
                  .font(.system(size: 16))
                  .foregroundColor(.white)
              }
              Text("我的积分")
                .font(.cardTitle)
                .fontWeight(.bold)
                .foregroundColor(.textPrimary)
            }
            Spacer()
          }

          // 积分信息
          if let userProfile = store.userProfile {
            VStack(spacing: NeumorphicDesign.smallSpacing) {
              // 剩余积分 - 主要显示
              HStack {
                VStack(alignment: .leading, spacing: 4) {
                  Text("剩余积分")
                    .font(.customBody)
                    .foregroundColor(.textSecondary)
                  Text("\(userProfile.remainingCredits)")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.warmOrange)
                }

                Spacer()

                // 🌸 新拟物风格积分图标
                ZStack {
                  Circle()
                    .fill(
                      LinearGradient(
                        colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                      )
                    )
                    .frame(width: 60, height: 60)
                    .shadow(color: .shadowDark.opacity(0.15), radius: 6, x: 3, y: 3)
                    .shadow(color: .shadowLight, radius: 6, x: -3, y: -3)
                  Image(systemName: "star.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.warmOrange)
                }
              }

              // 总积分信息
              HStack {
                Text("总积分: \(userProfile.totalCredits)")
                  .font(.customCaption)
                  .foregroundColor(.textSecondary)

                Spacer()

                Text("已使用: \(userProfile.totalCredits - userProfile.remainingCredits)")
                  .font(.customCaption)
                  .foregroundColor(.textSecondary)
              }
              .padding(.top, 8)
            }
          } else if store.isLoadingProfile {
            // 加载状态
            HStack {
              ProgressView()
                .scaleEffect(0.8)
              Text("正在加载积分信息...")
                .font(.customBody)
                .foregroundColor(.textSecondary)
            }
            .frame(height: 60)
          } else {
            // 无数据状态
            HStack {
              Image(systemName: "star")
                .font(.system(size: 16))
                .foregroundColor(.textSecondary)
              Text("积分信息暂不可用")
                .font(.customBody)
                .foregroundColor(.textSecondary)
            }
            .frame(height: 60)
          }
        }
      }
    }
  }

  // MARK: - 🌸 新拟物风格账户状态卡片
  
  @ViewBuilder
  private var accountStatusCard: some View {
    WithPerceptionTracking {
      ContentCard(style: .soft) {
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          // 卡片标题
          HStack {
            Text("账户状态")
              .font(.cardTitle)
              .fontWeight(.bold)
              .foregroundColor(.textPrimary)
            Spacer()
          }

          // 登录状态
          HStack(spacing: NeumorphicDesign.smallSpacing) {
            ZStack {
              Circle()
                .fill(
                  LinearGradient(
                    colors: [.successSoft.opacity(0.8), .successSoft.opacity(0.6)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                )
                .frame(width: 32, height: 32)
                .shadow(color: .successSoft.opacity(0.3), radius: 4, x: 0, y: 2)
              Image(systemName: "checkmark")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 2) {
              Text("登录状态")
                .font(.customBodyMedium)
                .foregroundColor(.textPrimary)
              
              Text("已登录")
                .font(.customBody)
                .foregroundColor(.successSoft)
            }
            
            Spacer()
          }
          
          Rectangle()
            .fill(Color.shadowDark.opacity(0.1))
            .frame(height: 1)
          
          // 订阅状态
          HStack(spacing: NeumorphicDesign.smallSpacing) {
            ZStack {
              Circle()
                .fill(
                  LinearGradient(
                    colors: [subscriptionStatusColor.opacity(0.8), subscriptionStatusColor.opacity(0.6)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                )
                .frame(width: 32, height: 32)
                .shadow(color: subscriptionStatusColor.opacity(0.3), radius: 4, x: 0, y: 2)
              Image(systemName: subscriptionStatusIcon)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: 2) {
              Text("订阅状态")
                .font(.customBodyMedium)
                .foregroundColor(.textPrimary)
              
              Text(subscriptionStatusText)
                .font(.customBody)
                .foregroundColor(subscriptionStatusColor)
                .onAppear {
                  print("🔍 ProfileSwiftUI: 当前订阅状态 = \(store.user?.subscriptionStatus.displayName ?? "nil")")
                }
                .onChange(of: store.user?.subscriptionStatus) { newValue in
                  print("🔄 ProfileSwiftUI: 订阅状态发生变化")
                  print("   新状态: \(newValue?.displayName ?? "nil")")
                }
            }
            
            Spacer()

            // 按钮区域
            HStack(spacing: 8) {
              if store.user?.subscriptionStatus == .free {
                PrimaryButton(
                  title: store.isLoading ? "处理中..." : "升级",
                  isLoading: store.isLoading,
                  action: {
                    store.send(ProfileCore.Profile.Action.upgradeSubscription)
                  }
                )
                .frame(width: 80, height: 32)
              }

              // 开发环境重置按钮
              #if DEBUG
              Button(action: {
                store.send(ProfileCore.Profile.Action.resetSubscriptionForTesting)
              }) {
                HStack(spacing: 4) {
                  Image(systemName: "arrow.counterclockwise")
                    .font(.system(size: 12))
                  Text("重置")
                }
              }
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.warmOrange)
              .padding(.horizontal, 12)
              .padding(.vertical, 6)
              .background(Color.warmOrange.opacity(0.1))
              .overlay(
                RoundedRectangle(cornerRadius: 6)
                  .stroke(Color.warmOrange.opacity(0.3), lineWidth: 1)
              )
              .cornerRadius(6)
              #endif
            }
          }
        }
      }
    }
  }
  
  // MARK: - 🌸 新拟物风格Pro升级推广卡片
  
  @ViewBuilder
  private var proUpgradeCard: some View {
    ContentCard(style: .elevated) {
      VStack(spacing: NeumorphicDesign.mediumSpacing) {
        // 卡片标题和图标
        HStack {
          ZStack {
            Circle()
              .fill(
                LinearGradient(
                  colors: [.softPink, .warmOrange],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
              .frame(width: 40, height: 40)
              .shadow(color: .softPink.opacity(0.3), radius: 6, x: 0, y: 3)
            Image(systemName: "crown.fill")
              .font(.system(size: 20, weight: .bold))
              .foregroundColor(.white)
          }

          VStack(alignment: .leading, spacing: 4) {
            Text("升级到 Pro 版本")
              .font(.cardTitle)
              .fontWeight(.bold)
              .foregroundColor(.textPrimary)
            
            Text("解锁所有高级模板和功能")
              .font(.customBody)
              .foregroundColor(.textSecondary)
          }
          
          Spacer()
        }
        
        // 功能列表
        VStack(alignment: .leading, spacing: 8) {
          ProFeatureRow(icon: "sparkles", text: "所有高级婚纱照模板")
          ProFeatureRow(icon: "wand.and.rays", text: "AI增强处理效果")
          ProFeatureRow(icon: "photo.stack", text: "无限制照片处理")
          ProFeatureRow(icon: "icloud.and.arrow.up", text: "云端存储同步")
        }
        
        // 升级按钮
        PrimaryButton(
          title: store.isLoading ? "正在升级..." : "立即升级",
          isLoading: store.isLoading,
          action: {
            store.send(ProfileCore.Profile.Action.upgradeSubscription)
          }
        )
      }
    }
  }

  // MARK: - 🌸 新拟物风格应用设置
  
  @ViewBuilder
  private var appSettingsSection: some View {
    VStack(spacing: NeumorphicDesign.smallSpacing) {
      // 标题
      HStack {
        Text("应用设置")
          .font(.system(size: 20, weight: .bold))
          .foregroundColor(.textPrimary)
        Spacer()
      }
      
      // 设置项
      ContentCard(style: .soft) {
        VStack(spacing: 0) {
          SettingsActionRow(
            icon: "bell.fill",
            iconColor: .warmOrange,
            title: "通知设置",
            action: {
              print("🔔 通知设置按钮被点击")
              store.send(ProfileCore.Profile.Action.showNotificationSettings)
            }
          )
          
          SettingsDivider()
          
        //   SettingsActionRow(
        //     icon: "lock.fill",
        //     iconColor: .softPink,
        //     title: "隐私设置",
        //     action: { store.send(ProfileCore.Profile.Action.showPrivacySettings) }
        //   )
          
        //   SettingsDivider()
          
        //   SettingsActionRow(
        //     icon: "photo.fill",
        //     iconColor: .successSoft,
        //     title: "照片质量",
        //     action: { store.send(ProfileCore.Profile.Action.showPhotoQualitySettings) }
        //   )
          
        //   SettingsDivider()
          
        //   SettingsActionRow(
        //     icon: "icloud.fill",
        //     iconColor: .softPink,
        //     title: "云同步",
        //     action: { store.send(ProfileCore.Profile.Action.showCloudSyncSettings) }
        //   )
        }
      }
    }
  }

  // MARK: - 🌸 新拟物风格关于应用
  
  @ViewBuilder
  private var aboutSection: some View {
    VStack(spacing: NeumorphicDesign.smallSpacing) {
      // 标题
      HStack {
        Text("关于应用")
          .font(.system(size: 20, weight: .bold))
          .foregroundColor(.textPrimary)
        Spacer()
      }
      
      // 关于项
      ContentCard(style: .soft) {
        VStack(spacing: 0) {
          SettingsVersionRow(
            icon: "info.circle.fill",
            iconColor: .softPink,
            title: "版本信息",
            version: "v1.0.0",
            action: { store.send(ProfileCore.Profile.Action.showVersionInfo) }
          )
          
          SettingsDivider()
          
          SettingsActionRow(
            icon: "star.fill",
            iconColor: .warmOrange,
            title: "评价应用",
            action: { store.send(ProfileCore.Profile.Action.showRating) }
          )
          
          SettingsDivider()
          
          SettingsActionRow(
            icon: "square.and.arrow.up",
            iconColor: .successSoft,
            title: "分享应用",
            action: { store.send(ProfileCore.Profile.Action.shareApp) }
          )
          
          // SettingsDivider()
          
          // SettingsActionRow(
          //   icon: "questionmark.circle.fill",
          //   iconColor: .warmOrange,
          //   title: "帮助中心",
          //   action: { store.send(ProfileCore.Profile.Action.showHelp) }
          // )
          
          SettingsDivider()
          
          SettingsActionRow(
            icon: "exclamationmark.bubble.fill",
            iconColor: .errorSoft,
            title: "反馈问题",
            action: { store.send(ProfileCore.Profile.Action.showFeedback) }
          )
        }
      }
    }
  }
  
  // MARK: - 🌸 新拟物风格账户操作
  
  @ViewBuilder
  private var accountActionsSection: some View {
    VStack(spacing: NeumorphicDesign.mediumSpacing) {
      // 退出登录按钮
      Button(action: { store.send(ProfileCore.Profile.Action.logoutButtonTapped) }) {
        HStack {
          Image(systemName: "rectangle.portrait.and.arrow.right")
            .font(.system(size: 16, weight: .medium))
          
          Text("退出登录")
            .font(.customBodyMedium)
            .fontWeight(.semibold)
          
          Spacer()
        }
        .foregroundColor(.white)
        .padding(.vertical, NeumorphicDesign.mediumPadding)
        .padding(.horizontal, NeumorphicDesign.largePadding)
        .background(
          RoundedRectangle(cornerRadius: 12)
            .fill(Color.black)
        )
      }
      .disabled(store.isLoading)
      
      // 删除账户按钮
    //   Button(action: { store.send(ProfileCore.Profile.Action.deleteAccountButtonTapped) }) {
    //     HStack {
    //       Image(systemName: "trash")
    //         .font(.system(size: 14, weight: .medium))
          
    //       Text("删除账户")
    //         .font(.system(size: 16, weight: .medium))
    //     }
    //     .foregroundColor(.errorSoft)
    //   }
    //   .disabled(store.isLoading)
    }
  }

  // MARK: - 计算属性
  
  private var subscriptionStatusColor: Color {
    switch store.user?.subscriptionStatus {
    case .premium:
      return .softPink
    case .free, .expired:
      return .textSecondary
    case .none:
      return .textSecondary
    }
  }
  
  private var subscriptionStatusIcon: String {
    switch store.user?.subscriptionStatus {
    case .premium:
      return "crown.fill"
    case .free, .expired:
      return "person.crop.circle"
    case .none:
      return "person.crop.circle"
    }
  }
  
  private var subscriptionStatusText: String {
    switch store.user?.subscriptionStatus {
    case .premium(let expiryDate, _):
      let formatter = DateFormatter()
      formatter.dateStyle = .medium
      formatter.locale = Locale(identifier: "zh_CN")
      return "高级版 Pro (到期: \(formatter.string(from: expiryDate)))"
    case .free:
      return "免费版"
    case .expired:
      return "订阅已过期"
    case .none:
      return "未知"
    }
  }
  
  // MARK: - 辅助函数

  private func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.locale = Locale(identifier: "zh_CN")
    return formatter.string(from: date)
  }

  private func formatAPIDate(_ dateString: String) -> String {
    // API返回的日期格式: "2025-08-01T02:26:27.685162"
    let inputFormatter = DateFormatter()
    inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
    inputFormatter.locale = Locale(identifier: "en_US_POSIX")

    if let date = inputFormatter.date(from: dateString) {
      let outputFormatter = DateFormatter()
      outputFormatter.dateStyle = .medium
      outputFormatter.locale = Locale(identifier: "zh_CN")
      return outputFormatter.string(from: date)
    } else {
      // 如果解析失败，尝试其他格式或返回原始字符串
      return dateString
    }
  }
}

// MARK: - Settings Components

struct SettingsActionRow: View {
  let icon: String
  let iconColor: Color
  let title: String
  let action: () -> Void

  var body: some View {
    Button(action: {
      print("🎯 SettingsActionRow 按钮被点击: \(title)")
      action()
    }) {
      HStack(spacing: NeumorphicDesign.smallSpacing) {
        // 🌸 新拟物风格图标
        ZStack {
          RoundedRectangle(cornerRadius: 8)
            .fill(
              LinearGradient(
                colors: [iconColor.opacity(0.8), iconColor.opacity(0.6)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 32, height: 32)
            .shadow(color: iconColor.opacity(0.3), radius: 4, x: 0, y: 2)

          Image(systemName: icon)
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.white)
        }

        // Title
        Text(title)
          .font(.customBodyMedium)
          .foregroundColor(.textPrimary)

        Spacer()

        // Arrow
        Image(systemName: "chevron.right")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.textSecondary)
      }
      .padding(.horizontal, NeumorphicDesign.mediumPadding)
      .padding(.vertical, NeumorphicDesign.smallPadding)
    }
    .buttonStyle(PlainButtonStyle())
  }
}

struct SettingsVersionRow: View {
  let icon: String
  let iconColor: Color
  let title: String
  let version: String
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      HStack(spacing: NeumorphicDesign.smallSpacing) {
        // 🌸 新拟物风格图标
        ZStack {
          RoundedRectangle(cornerRadius: 8)
            .fill(
              LinearGradient(
                colors: [iconColor.opacity(0.8), iconColor.opacity(0.6)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 32, height: 32)
            .shadow(color: iconColor.opacity(0.3), radius: 4, x: 0, y: 2)

          Image(systemName: icon)
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.white)
        }

        // Title
        Text(title)
          .font(.customBodyMedium)
          .foregroundColor(.textPrimary)

        Spacer()

        // Version
        Text(version)
          .font(.customBody)
          .foregroundColor(.textSecondary)
      }
      .padding(.horizontal, NeumorphicDesign.mediumPadding)
      .padding(.vertical, NeumorphicDesign.smallPadding)
    }
    .buttonStyle(PlainButtonStyle())
  }
}

struct SettingsDivider: View {
  var body: some View {
    Rectangle()
      .fill(Color.shadowDark.opacity(0.1))
      .frame(height: 1)
      .padding(.leading, 56) // Align with text after icon
  }
}

// MARK: - Pro Feature Row Component

struct ProFeatureRow: View {
  let icon: String
  let text: String
  
  var body: some View {
    HStack(spacing: NeumorphicDesign.smallSpacing) {
      Image(systemName: icon)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.softPink)
        .frame(width: 16)
      
      Text(text)
        .font(.customBody)
        .foregroundColor(.textPrimary)
      
      Spacer()
    }
  }
}

// MARK: - Previews

#Preview("Settings View") {
  NavigationView {
    ProfileView(
      store: Store(initialState: ProfileCore.Profile.State()) {
        ProfileCore.Profile()
      }
    )
  }
}