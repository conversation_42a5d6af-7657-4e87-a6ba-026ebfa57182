# 订阅状态重置功能 - 开发测试专用

## 🎯 **功能概述**

为了方便开发阶段测试订阅功能，在个人中心的订阅状态部分添加了一个"重置"按钮，可以快速清除用户的订阅信息，将订阅状态重置为免费版。

## 🔧 **实现细节**

### 1. **ProfileCore 新增 Action**
```swift
#if DEBUG
case resetSubscriptionForTesting
#endif
```

### 2. **重置逻辑**
- 将用户订阅状态重置为 `.free`
- 清除 UserDefaults 中的订阅状态
- 清除 Keychain 中的订阅状态
- 更新用户信息并持久化保存

### 3. **UI 界面**
在订阅状态右侧添加了一个橙色的重置按钮：
- 图标：`arrow.counterclockwise`
- 文字：重置
- 样式：橙色边框，半透明背景

## 🚀 **使用方法**

1. 在开发环境中运行应用
2. 进入个人中心页面
3. 在订阅状态部分可以看到"重置"按钮
4. 点击重置按钮即可清除订阅状态

## 🔒 **安全特性**

### 仅在开发环境可用
```swift
#if DEBUG
// 重置按钮和相关逻辑
#endif
```

### 生产环境自动移除
- 使用 `#if DEBUG` 编译条件
- 生产版本构建时自动排除此功能
- 确保用户无法在正式版本中误操作

## 📱 **界面效果**

### 免费用户界面
```
[订阅图标] 订阅状态          [升级] [重置]
          免费用户
```

### 付费用户界面
```
[订阅图标] 订阅状态               [重置]
          高级会员
```

## 🧪 **测试场景**

### 1. **订阅流程测试**
1. 用户处于免费状态
2. 完成订阅购买
3. 验证订阅状态更新
4. 使用重置按钮恢复免费状态
5. 重复测试流程

### 2. **状态持久化测试**
1. 完成订阅
2. 重启应用
3. 验证订阅状态保持
4. 使用重置按钮清除
5. 重启应用验证状态已清除

### 3. **多场景测试**
- 单次购买重置
- 月度订阅重置
- 年度订阅重置
- 过期订阅重置

## ⚠️ **注意事项**

### 开发阶段
- ✅ 可以随意使用重置功能测试
- ✅ 不会影响真实的 App Store 交易
- ✅ 只清除本地存储的订阅状态

### 生产环境
- ❌ 重置按钮不会出现
- ❌ 重置 Action 不会被编译
- ❌ 用户无法访问此功能

## 🔄 **重置流程**

1. **点击重置按钮**
   ```swift
   store.send(ProfileCore.Profile.Action.resetSubscriptionForTesting)
   ```

2. **执行重置逻辑**
   - 更新用户状态为免费版
   - 清除 UserDefaults 存储
   - 清除 Keychain 存储
   - 保存更新后的用户信息

3. **界面自动更新**
   - 订阅状态显示为"免费用户"
   - 显示"升级"按钮
   - 重置按钮保持可用

## 📝 **日志输出**

重置过程会输出详细的调试信息：
```
🧪 [DEBUG] 重置订阅状态用于测试
🧪 [DEBUG] 订阅状态已重置为免费版
🧪 [DEBUG] 订阅状态重置已持久化保存
🧪 [DEBUG] 订阅状态重置完成
```

## 🚀 **未来改进**

1. **批量测试数据**：可以考虑添加快速设置不同订阅状态的功能
2. **测试场景预设**：预设常见的测试场景组合
3. **重置确认**：添加重置确认对话框防止误操作
4. **测试统计**：记录测试过程中的操作统计

---

**重要提醒**：此功能仅用于开发测试，生产环境会自动移除，请放心使用！ 🎉
