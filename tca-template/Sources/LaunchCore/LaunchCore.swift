import Foundation
import ComposableArchitecture
import LoggingClient
import AuthenticationClient
import UserStorageClient
import UserStateCore
import KeychainClient

// MARK: - Launch Screen Feature

@Reducer
public struct Launch: Sendable {
  @ObservableState
  public struct State: Equatable {
    public var isCheckingAuthentication = true
    public var authenticationError: String?
    
    public init() {}
  }
  
  public enum Action: Sendable {
    case onAppear
    case checkAuthenticationStatus
    case userIsAuthenticated(User, String) // user, token
    case userNotAuthenticated
    case authenticationCheckFailed(String)
  }
  
  @Dependency(\.loggingClient) var logger
  @Dependency(\.authenticationClient) var authenticationClient
  @Dependency(\.userStorageClient) var userStorageClient
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    Reduce { state, action in
      switch action {
      case .onAppear:
        logger.info(.app, "Launch screen appeared, checking authentication")
        return .send(.checkAuthenticationStatus)
        
      case .checkAuthenticationStatus:
        state.isCheckingAuthentication = true
        state.authenticationError = nil
        
        return .run { send in
          @Dependency(\.userStorageClient) var userStorageClient
          @Dependency(\.keychainClient) var keychain
          
          do {
            print("🚀 LaunchCore: Starting authentication check")
            print("📱 应用重启，检查是否有保存的登录状态...")
            
            // 使用调试工具检查Keychain状态
            print("🔍 使用KeychainDebugger检查完整状态...")
            KeychainDebugger.debugKeychainContents()
            
            // 简化启动时测试，避免并发问题
            // print("🧪 LaunchCore: 运行启动时Keychain测试...")
            // KeychainTestTool.testKeychainLoadOnStartup()
            
            // 直接检查Keychain中的原始数据
            print("🔍 直接检查Keychain原始数据...")
            do {
              let rawToken = try await keychain.loadString(forKey: KeychainKeys.accessToken)
              let rawUserData = try await keychain.load(StoredUserData.self, forKey: KeychainKeys.userID)
              
              print("   原始Token: \(rawToken != nil ? "存在" : "不存在")")
              if let token = rawToken {
                print("   Token内容: \(token.prefix(20))...")
              }
              
              print("   原始用户数据: \(rawUserData != nil ? "存在" : "不存在")")
              if let userData = rawUserData {
                print("   用户数据: \(userData.displayName) (\(userData.email))")
              }
            } catch {
              print("❌ 直接检查Keychain失败: \(error)")
            }
            
            // Check if user is logged in using Keychain storage
            let isLoggedIn = await userStorageClient.isUserLoggedIn()
            print("🔍 LaunchCore: UserStorageClient.isLoggedIn = \(isLoggedIn)")
            
            if isLoggedIn {
              // Try to load user data from Keychain
              if let (user, token) = try await userStorageClient.loadUser() {
                print("✅ LaunchCore: User data loaded from Keychain successfully")
                print("   用户: \(user.displayName) (\(user.email))")
                print("   Token: \(token.prefix(20))...")
                print("   订阅状态: \(user.subscriptionStatus.displayName)")
                
                // Update last login date
                try await userStorageClient.updateLastLoginDate()
                
                print("🎉 LaunchCore: 自动登录成功，跳转到主界面")
                await send(.userIsAuthenticated(user, token))
              } else {
                print("❌ LaunchCore: isLoggedIn=true但无法加载用户数据，清理无效状态")
                // Clean up invalid state
                try? await userStorageClient.deleteUser()
                await send(.userNotAuthenticated)
              }
            } else {
              print("❌ LaunchCore: 没有找到有效的登录状态，显示登录界面")
              await send(.userNotAuthenticated)
            }
          } catch {
            print("💥 LaunchCore: Error during auth check: \(error)")
            print("   错误类型: \(type(of: error))")
            print("   错误描述: \(error.localizedDescription)")
            await send(.authenticationCheckFailed(error.localizedDescription))
          }
        }
        
      case .userIsAuthenticated(let user, _):
        logger.info(.app, "User has valid authentication: \(user.displayName)")
        state.isCheckingAuthentication = false
        return .none
        
      case .userNotAuthenticated:
        logger.info(.app, "User authentication not found")
        state.isCheckingAuthentication = false
        return .none
        
      case .authenticationCheckFailed(let error):
        logger.error(.app, "Authentication check failed: \(error)")
        state.isCheckingAuthentication = false
        state.authenticationError = error
        // Default to not authenticated on error
        return .send(.userNotAuthenticated)
      }
    }
  }
}