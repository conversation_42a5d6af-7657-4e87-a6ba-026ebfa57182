import SwiftUI
import Composable<PERSON>rchitecture
import CommonUI
import ImageTypeSelectionCore
import Perception
#if canImport(UIKit)
import UIKit
#endif

public struct ImageTypeSelectionView: View {
  @Perception.Bindable var store: StoreOf<ImageTypeSelection>
  
  private let columns = [
    GridItem(.flexible(), spacing: 16),
    GridItem(.flexible(), spacing: 16)
  ]
  
  public init(store: StoreOf<ImageTypeSelection>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      ZStack {
        // 🌸 新拟物风格背景
        Color.backgroundGradient.ignoresSafeArea()
        
        if store.isLoading {
          loadingView
        } else if let error = store.error {
          errorView(error)
        } else {
          mainContent
        }
      }
      .navigationTitle("选择您的风格")
      .neumorphicNavigationStyle()
      .onAppear {
        print("🎨 ImageTypeSelectionView appeared")
        store.send(.onAppear)
      }
      .sheet(isPresented: Binding(
        get: { store.isShowingGenerationConfirmation },
        set: { _ in store.send(.hideGenerationConfirmation) }
      )) {
        GenerationConfirmationSheet(store: store)
      }
    }
  }
  
  private var loadingView: some View {
    VStack(spacing: NeumorphicDesign.largeSpacing) {
      EnhancedLoadingView(message: "正在加载精美模板...")

      Text("为您准备精彩风格")
        .font(.customBody)
        .foregroundColor(.textSecondary)
        .multilineTextAlignment(.center)
    }
    .padding(NeumorphicDesign.largePadding)
  }
  
  private func errorView(_ error: String) -> some View {
    ContentCard(style: .soft) {
      VStack(spacing: NeumorphicDesign.largeSpacing) {
        // 🌸 新拟物风格错误图标
        ZStack {
          Circle()
            .fill(
              LinearGradient(
                colors: [.warningSoft.opacity(0.8), .warningSoft.opacity(0.6)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 80, height: 80)
            .shadow(color: .warningSoft.opacity(0.3), radius: 8, x: 0, y: 4)

          Image(systemName: "exclamationmark.triangle.fill")
            .font(.system(size: 32))
            .foregroundColor(.white)
        }

        VStack(spacing: NeumorphicDesign.smallSpacing) {
          Text("无法加载模板")
            .font(.cardTitle)
            .foregroundColor(.textPrimary)

          Text(error)
            .font(.customBody)
            .foregroundColor(.textSecondary)
            .multilineTextAlignment(.center)
        }

        PrimaryButton(
          title: "重试",
          style: .primary,
          action: { store.send(.refreshTemplates) }
        )
      }
    }
    .padding(NeumorphicDesign.largePadding)
  }
  
  private var mainContent: some View {
    ScrollView {
      VStack(spacing: NeumorphicDesign.largeSpacing) {
        // 🌸 新拟物风格头部区域
        headerSection
        
        // 模板网格
        LazyVGrid(columns: columns, spacing: NeumorphicDesign.largeSpacing) {
          ForEach(store.templates, id: \.id) { template in
            TemplateCard(
              template: template,
              isGuestMode: store.isGuestMode
            ) {
              print("🎯 Action closure called for template: \(template.name)")
              store.send(.templateSelected(template))
            }
          }
        }
      }
      .padding(NeumorphicDesign.largePadding)
    }
    .refreshable {
      store.send(.refreshTemplates)
    }
  }
  
  // MARK: - 🌸 新拟物风格头部区域
  
  private var headerSection: some View {
    VStack(spacing: NeumorphicDesign.largeSpacing) {
      // 新拟物风格图标
      ZStack {
        Circle()
          .fill(
            LinearGradient(
              colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 100, height: 100)
          .shadow(color: .shadowDark.opacity(0.2), radius: 12, x: 4, y: 4)
          .shadow(color: .shadowLight, radius: 12, x: -4, y: -4)

        Image(systemName: "paintbrush.pointed.fill")
          .font(.system(size: 40, weight: .light))
          .foregroundStyle(
            LinearGradient(
              colors: [.softPink, .warmOrange],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
      }
      
      VStack(spacing: NeumorphicDesign.smallSpacing) {
        Text("选择您的风格")
          .font(.welcomeTitle)
          .fontWeight(.bold)
          .foregroundColor(.textPrimary)
        
        Text("AI将根据您的选择生成专属婚纱照")
          .font(.welcomeSubtitle)
          .foregroundColor(.textSecondary)
          .multilineTextAlignment(.center)
          .lineSpacing(4)
      }
    }
  }
}

// MARK: - 🌸 新拟物风格模板卡片

private struct TemplateCard: View {
  let template: ImageTemplate
  let isGuestMode: Bool
  let action: () -> Void
  
  @State private var isPressed = false
  
  var body: some View {
    Button(action: {
      print("🎨 Template card button pressed: \(template.name)")
      
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .light)
      impactFeedback.impactOccurred()
      #endif
      
      print("✅ Calling action closure for: \(template.name)")
      action()
    }) {
      VStack(alignment: .leading, spacing: NeumorphicDesign.mediumSpacing) {
        // 🌸 新拟物风格预览区域
        ZStack {
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .fill(
              LinearGradient(
                colors: gradientColors,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(height: 120)
            .shadow(color: .shadowDark.opacity(0.15), radius: 6, x: 0, y: 3)
          
          // 主图标
          VStack(spacing: 8) {
            Image(systemName: template.category.systemImage)
              .font(.system(size: 28, weight: .light))
              .foregroundColor(.white)
            
            if template.isPremium {
              HStack(spacing: 4) {
                Image(systemName: "crown.fill")
                  .font(.system(size: 12))
                Text("VIP")
                  .font(.customTag)
                  .fontWeight(.bold)
              }
              .foregroundColor(.white)
              .padding(.horizontal, 8)
              .padding(.vertical, 4)
              .background(
                RoundedRectangle(cornerRadius: 8)
                  .fill(Color.black.opacity(0.3))
              )
            }
          }
        }
        
        // 🌸 新拟物风格信息区域
        VStack(alignment: .leading, spacing: NeumorphicDesign.smallSpacing) {
          HStack {
            Text(template.name)
              .font(.customHeadline)
              .fontWeight(.semibold)
              .lineLimit(1)
              .foregroundColor(.textPrimary)
            
            Spacer()
            
            if template.isPremium {
              ColorfulTag(
                text: "专业版",
                backgroundColor: .warmOrange,
                textColor: .white
              )
            }
          }
          
          Text(template.description)
            .font(.customCaption)
            .foregroundColor(.textSecondary)
            .lineLimit(2)
          
          HStack {
            ColorfulTag(
              text: template.category.rawValue,
              backgroundColor: categoryColor.opacity(0.8),
              textColor: .white
            )
            
            Spacer()
            
            HStack(spacing: 4) {
              Image(systemName: "clock")
                .font(.system(size: 10))
              Text("\(Int(template.estimatedGenerationTime))s")
                .font(.customCaption)
            }
            .foregroundColor(.textTertiary)
          }
        }
      }
      .padding(NeumorphicDesign.mediumPadding)
      .background(
        ZStack {
          // 新拟物背景
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .fill(
              LinearGradient(
                colors: [.white.opacity(0.95), .softBeige.opacity(0.8)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
          
          // 内阴影效果
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .stroke(Color.white.opacity(0.8), lineWidth: 1)
            .blur(radius: 0.5)
            .offset(x: -1, y: -1)
          
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .stroke(Color.shadowInner, lineWidth: 1)
            .blur(radius: 0.5)
            .offset(x: 1, y: 1)
        }
      )
      .scaleEffect(isPressed ? 0.98 : 1.0)
      .shadow(
        color: .shadowDark.opacity(0.1),
        radius: isPressed ? 4 : 8,
        x: 0,
        y: isPressed ? 2 : 4
      )
    }
    .buttonStyle(PlainButtonStyle())
    .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
      withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
        isPressed = pressing
      }
    }, perform: {})
  }
  
  private var gradientColors: [Color] {
    switch template.category {
    case .wedding: return [.accentPink, .accentPurple]
    case .portrait: return [.accentBlue, .primaryAccent]
    case .fashion: return [.accentPurple, .accentPink]
    case .vintage: return [.accentOrange, .secondaryAccent]
    case .artistic: return [.accentOrange, .accentPink]
    case .elegant: return [.primaryAccent, .accentBlue]
    }
  }
  
  private var categoryColor: Color {
    switch template.category {
    case .wedding: return .accentPink
    case .portrait: return .accentBlue
    case .fashion: return .accentPurple
    case .vintage: return .accentOrange
    case .artistic: return .accentOrange
    case .elegant: return .primaryAccent
    }
  }
}

// MARK: - Generation Confirmation Sheet

struct GenerationConfirmationSheet: View {
  let store: StoreOf<ImageTypeSelection>

  var body: some View {
    NavigationView {
      VStack(spacing: 24) {
        // Header Section
        VStack(spacing: 12) {
          Image(systemName: "sparkles")
            .font(.system(size: 48))
            .foregroundColor(.pink)

          Text("确认生成")
            .font(.title2)
            .fontWeight(.bold)

          Text("即将为您生成专属的婚纱照片")
            .font(.subheadline)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }

        // Selected Template Section
        if let selectedTemplate = store.selectedTemplate {
          VStack(alignment: .leading, spacing: 16) {
            Text("选择的风格")
              .font(.headline)

            HStack(spacing: 16) {
              // Template Preview
              ZStack {
                RoundedRectangle(cornerRadius: 12)
                  .fill(
                    LinearGradient(
                      colors: gradientColors(for: selectedTemplate.category),
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    )
                  )
                  .frame(width: 80, height: 80)

                VStack {
                  Image(systemName: selectedTemplate.category.systemImage)
                    .font(.system(size: 24))
                    .foregroundColor(.white)

                  if selectedTemplate.isPremium {
                    Image(systemName: "crown.fill")
                      .font(.caption)
                      .foregroundColor(.yellow)
                  }
                }
              }

              // Template Details
              VStack(alignment: .leading, spacing: 4) {
                HStack {
                  Text(selectedTemplate.name)
                    .font(.headline)

                  if selectedTemplate.isPremium {
                    Image(systemName: "crown.fill")
                      .foregroundColor(.yellow)
                      .font(.caption)
                  }
                }

                Text(selectedTemplate.description)
                  .font(.subheadline)
                  .foregroundColor(.secondary)
                  .lineLimit(2)

                Text("分类: \(selectedTemplate.category.rawValue)")
                  .font(.caption)
                  .foregroundColor(.blue)
              }

              Spacer()
            }
            .padding(16)
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
          }
        }

        // Generation Info Section
        VStack(alignment: .leading, spacing: 12) {
          Text("生成信息")
            .font(.headline)

          VStack(spacing: 8) {
            HStack {
              Image(systemName: "clock")
                .foregroundColor(.blue)
              Text("预计时间: 30-60秒")
                .font(.subheadline)
              Spacer()
            }

            HStack {
              Image(systemName: "photo.on.rectangle")
                .foregroundColor(.green)
              Text("将生成高质量婚纱照片")
                .font(.subheadline)
              Spacer()
            }

            HStack {
              Image(systemName: "checkmark.circle")
                .foregroundColor(.orange)
              Text("可保存到相册")
                .font(.subheadline)
              Spacer()
            }
          }
          .padding(16)
          .background(Color.gray.opacity(0.05))
          .cornerRadius(12)
        }

        Spacer()

        // Action Buttons
        VStack(spacing: 12) {
          Button("确认生成") {
            store.send(.confirmGeneration)
          }
          .font(.headline)
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .padding(.vertical, 16)
          .background(
            LinearGradient(
              colors: [.pink, .purple],
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(12)

          Button("取消") {
            store.send(.hideGenerationConfirmation)
          }
          .font(.subheadline)
          .foregroundColor(.secondary)
        }
      }
      .padding(20)
      .navigationTitle("确认生成")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .toolbar {
        ToolbarItem(placement: .automatic) {
          Button("取消") {
            store.send(.hideGenerationConfirmation)
          }
        }
      }
    }
  }

  private func gradientColors(for category: ImageTemplate.Category) -> [Color] {
    switch category {
    case .wedding: return [.accentPink, .accentPurple]
    case .portrait: return [.accentBlue, .primaryAccent]
    case .fashion: return [.accentPurple, .accentPink]
    case .vintage: return [.accentOrange, .secondaryAccent]
    case .artistic: return [.accentOrange, .accentPink]
    case .elegant: return [.primaryAccent, .accentBlue]
    }
  }
}

#Preview {
  ImageTypeSelectionView(
    store: Store(initialState: ImageTypeSelection.State()) {
      ImageTypeSelection()
    }
  )
}