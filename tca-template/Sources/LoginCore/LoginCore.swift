import AuthenticationClient
import ComposableArchitecture
import Dispatch
import TwoFactorCore
import UserStorageClient
import UserStateCore
import KeychainClient
import Foundation

@Reducer
public struct Login: Sendable {
  @ObservableState
  public struct State: Equatable {
    @Presents public var alert: AlertState<Action.Alert>?
    public var email = ""
    public var isFormValid = false
    public var isLoginRequestInFlight = false
    public var password = ""
    @Presents public var twoFactor: TwoFactor.State?

    public init() {}
  }

  public enum Action: Sendable, ViewAction {
    case alert(PresentationAction<Alert>)
    case loginResponse(Result<AuthenticationResponse, any Error>)
    case appleSignIn(AppleIDCredential)
    case twoFactor(PresentationAction<TwoFactor.Action>)
    case view(View)

    public enum Alert: Equatable, Sendable {}

    @CasePathable
    public enum View: BindableAction, Sendable {
      case binding(BindingAction<State>)
      case loginButtonTapped
    }
  }

  @Dependency(\.authenticationClient) var authenticationClient
  @Dependency(\.userStorageClient) var userStorageClient

  public init() {}

  public var body: some Reducer<State, Action> {
    BindingReducer(action: \.view)
    Reduce { state, action in
      switch action {
      case .alert:
        return .none

      case let .appleSignIn(credential):
        state.isLoginRequestInFlight = true
        return .run { send in
          await send(
            .loginResponse(
              Result {
                try await self.authenticationClient.signInWithApple(credential)
              }
            )
          )
        }

      case let .loginResponse(.success(response)):
        state.isLoginRequestInFlight = false
        if response.twoFactorRequired {
          state.twoFactor = TwoFactor.State(token: response.token)
        } else {
          // Login successful without 2FA - save user data to Keychain
          if let user = response.user {
            return .run { send in
              do {
                print("💾 LoginCore: 开始保存用户数据到Keychain")
                print("   用户: \(user.displayName) (\(user.email))")
                print("   Token类型: \(response.token.contains("mock") ? "Mock Token" : "Real Token")")
                print("   Token: \(response.token.prefix(20))...")

                // 获取用户的真实订阅状态
                let subscriptionStatus = await fetchUserSubscriptionStatus(userId: user.id) ?? .free
                print("   订阅状态: \(subscriptionStatus.displayName)")

                // Convert AuthenticatedUser to User (if needed)
                let userForStorage = User(
                  id: user.id,
                  email: user.email,
                  displayName: user.displayName,
                  avatarURL: user.avatarURL,
                  createdAt: Date(), // Since we don't have this from AuthenticatedUser
                  subscriptionStatus: subscriptionStatus
                )
                
                print("🔐 LoginCore: 开始保存到Keychain...")
                // Save user and token to Keychain - 这是关键步骤
                try await userStorageClient.saveUser(userForStorage, response.token)
                print("✅ LoginCore: User data saved to Keychain successfully")
                print("   用户ID: \(userForStorage.id)")
                print("   邮箱: \(userForStorage.email)")
                print("   显示名: \(userForStorage.displayName)")
                print("   订阅状态: \(userForStorage.subscriptionStatus.displayName)")
                
                // 验证保存是否成功
                print("🔍 LoginCore: 验证Keychain保存结果...")
                if let (savedUser, savedToken) = try await userStorageClient.loadUser() {
                  print("✅ LoginCore: 验证保存成功，可以从Keychain读取用户数据")
                  print("   读取的用户: \(savedUser.displayName)")
                  print("   读取的Token: \(savedToken.prefix(20))...")
                  print("   读取的订阅状态: \(savedUser.subscriptionStatus.displayName)")
                } else {
                  print("❌ LoginCore: 验证失败，无法从Keychain读取用户数据")
                }
                
                // 使用调试工具检查完整的Keychain状态
                // print("🔍 LoginCore: 使用KeychainDebugger检查保存后的状态...")
                // KeychainDebugger.debugKeychainContents()
                
                // 运行测试工具验证保存
                // print("🧪 LoginCore: 运行KeychainTestTool验证...")
                // KeychainTestTool.testKeychainSaveAfterLogin()
                
              } catch {
                print("❌ LoginCore: Failed to save user data to Keychain: \(error)")
                print("   错误类型: \(type(of: error))")
                print("   错误描述: \(error.localizedDescription)")
                
                // 即使保存失败，也要记录详细信息
                if let keychainError = error as? UserStorageError {
                  print("   Keychain错误详情: \(keychainError)")
                }
                
                // Don't fail the login for this error, just log it
              }
            }
          } else {
            print("⚠️ LoginCore: Login successful but no user data provided")
          }
        }
        return .none

      case let .loginResponse(.failure(error)):
        state.alert = AlertState { TextState(error.localizedDescription) }
        state.isLoginRequestInFlight = false
        return .none

      case .twoFactor:
        return .none

      case .view(.binding):
        state.isFormValid = !state.email.isEmpty && !state.password.isEmpty
        return .none

      case .view(.loginButtonTapped):
        state.isLoginRequestInFlight = true
        return .run { [email = state.email, password = state.password] send in
          await send(
            .loginResponse(
              Result {
                try await self.authenticationClient.login(email: email, password: password)
              }
            )
          )
        }
      }
    }
    .ifLet(\.$alert, action: \.alert)
    .ifLet(\.$twoFactor, action: \.twoFactor) {
      TwoFactor()
    }
  }
}

// MARK: - Helper Functions

/// 从服务端获取用户的订阅状态
private func fetchUserSubscriptionStatus(userId: String) async -> SubscriptionStatus? {
  print("🔍 LoginCore: 获取用户订阅状态: \(userId)")

  // 1. 首先尝试从Keychain恢复订阅状态
  @Dependency(\.keychainClient) var keychain
  
  do {
    if let subscriptionData = try await keychain.load(KeychainKeys.subscriptionStatus),
       let savedSubscriptionStatus = try? JSONDecoder().decode(SubscriptionStatus.self, from: subscriptionData) {
      print("✅ LoginCore: 从Keychain恢复订阅状态: \(savedSubscriptionStatus.displayName)")

      // 检查订阅是否过期
      if savedSubscriptionStatus.isActive {
        return savedSubscriptionStatus
      } else {
        print("⚠️ LoginCore: 本地订阅状态已过期，重置为免费版")
        try await keychain.delete(KeychainKeys.subscriptionStatus)
        return .free
      }
    }
  } catch {
    print("❌ LoginCore: 从Keychain读取订阅状态失败: \(error)")
  }

  // 2. 如果本地没有，可以在这里添加服务端API调用
  // TODO: 实现服务端API调用获取用户订阅状态

  print("ℹ️ LoginCore: 无法获取订阅状态，默认为免费版")
  return .free
}
