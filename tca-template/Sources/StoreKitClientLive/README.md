# StoreKit Implementation - 订阅系统重构

## 🔧 **修复的问题**

### 1. **移除重复代码**
- ✅ 删除了 `SubscriptionCore.swift` 中的重复 StoreKit 实现
- ✅ 统一使用 `StoreKitClientLive.swift` 作为唯一的 StoreKit 实现
- ✅ 创建了 `ProductConfiguration` 统一管理产品配置

### 2. **实现真实支付流程**
- ✅ 使用 StoreKit 2 的 `Product.purchase()` 触发真实的 App Store 支付界面
- ✅ 添加了 `TransactionObserver` 监听后台订阅变化
- ✅ 实现了完整的交易验证和处理流程
- ✅ 支持 StoreKit Configuration 文件测试

### 3. **安全的订阅状态存储**
- ✅ 实现了 `KeychainSubscriptionStorage` 使用 Keychain 安全存储订阅状态
- ✅ 支持订阅状态的加密存储和恢复
- ✅ 自动处理订阅过期和续费

## 📱 **产品配置**

根据 `Configuration.storekit` 文件配置的产品：

| 产品ID | 名称 | 价格 | 描述 |
|--------|------|------|------|
| `com.wenhaofree.bridal.single_basic` | 单次生成 | ¥1.00 | 1次高清图片生成 |
| `com.wenhaofree.bridal.sub_monthly_44` | 高级月度订阅 | ¥28.00 | 每月40次生成，解锁所有功能 |
| `com.wenhaofree.bridal.sub_yearly_600` | 年度订阅 | ¥128.00 | 每年600次生成，享受最大优惠 |

## 🚀 **新功能**

### 1. **真实支付流程**
```swift
// 触发真实的 App Store 支付界面
let result = try await product.purchase()
```

### 2. **后台订阅监听**
```swift
// 自动监听订阅状态变化
let observer = TransactionObserver()
```

### 3. **安全存储**
```swift
// 使用 Keychain 安全存储订阅状态
await KeychainSubscriptionStorage.shared.saveSubscriptionStatus(status)
```

## 🧪 **测试支持**

### StoreKit Configuration 测试
- 在模拟器中自动检测 `Configuration.storekit` 文件
- 支持测试环境的模拟购买流程
- 完整的交易验证和处理

### 沙盒测试
- 自动检测沙盒环境
- 提供详细的调试信息
- 支持沙盒测试账号

## 🔒 **安全特性**

1. **交易验证**: 使用 `checkVerified()` 验证所有交易
2. **Keychain 存储**: 订阅状态加密存储在 Keychain 中
3. **自动过期检查**: 自动检查订阅是否过期
4. **后台更新**: 监听 App Store 的订阅状态变化

## 📋 **使用方法**

### 1. 加载产品
```swift
let products = try await storeKitClient.loadProducts()
```

### 2. 购买产品
```swift
let status = try await storeKitClient.purchase(product)
```

### 3. 恢复购买
```swift
let status = try await storeKitClient.restorePurchases()
```

### 4. 检查订阅状态
```swift
let status = await storeKitClient.checkSubscriptionStatus()
```

## ⚠️ **注意事项**

1. **App Store Connect 配置**: 确保所有产品已在 App Store Connect 中正确配置
2. **沙盒测试**: 使用沙盒测试账号进行测试
3. **证书配置**: 确保应用签名和证书配置正确
4. **网络连接**: 需要网络连接才能与 App Store 通信

## 🐛 **调试信息**

系统会输出详细的调试信息，包括：
- 🛒 产品加载状态
- 💳 购买流程进度
- 🔐 交易验证结果
- 📱 后台更新通知
- 🧪 测试环境检测

查看控制台输出以获取详细的调试信息。
