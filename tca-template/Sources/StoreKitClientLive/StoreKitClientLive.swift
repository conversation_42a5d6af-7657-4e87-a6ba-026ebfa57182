import Foundation
import StoreKit
import ComposableArchitecture
import SubscriptionCore

public typealias StoreKitClient = SubscriptionCore.StoreKitClient

// MARK: - Live StoreKit 2 Implementation

@available(iOS 15.0, macOS 12.0, *)
public struct LiveStoreKitClient {

  // MARK: - Live Implementation

  public static let live: SubscriptionCore.StoreKitClient = .init(
      loadProducts: { productIDs in
        print("🛒 Loading products from App Store...")

        do {
          let storeProducts = try await Product.products(for: productIDs)
          print("✅ Loaded \(storeProducts.count) products from App Store")

          // 验证产品配置
          for productID in productIDs {
            if !storeProducts.contains(where: { $0.id == productID }) {
              print("⚠️ Product ID '\(productID)' not found in App Store Connect")
            }
          }

          return storeProducts

        } catch {
          print("❌ Failed to load products: \(error)")
          print("❌ Error details: \(error.localizedDescription)")

          // Return empty array on error
          return []
        }
      },

      purchase: { product in
        print("💳 Starting purchase for: \(product.displayName)")
        print("💳 Product ID: \(product.id)")

        do {
          print("✅ Product: \(product.displayName) - \(product.displayPrice)")
          print("💰 Price: \(product.price) \(product.priceFormatStyle.currencyCode)")

          // 执行真实购买 - 这会触发App Store的支付界面
          print("💳 Initiating real App Store purchase...")
          let result = try await product.purchase()
          print("💳 Purchase result received")

          switch result {
          case .success(let verification):
            do {
              let transaction = try LiveStoreKitClient.checkVerified(verification)
              print("✅ Purchase successful: \(transaction.productID)")
              return .success(transaction)
            } catch {
              print("❌ Transaction verification failed: \(error)")
              throw SubscriptionCore.StoreKitClientError.verificationFailed
            }
          case .userCancelled:
            print("🚫 User cancelled the purchase")
            return .userCancelled
          case .pending:
            print("⏳ Purchase is pending approval")
            return .pending
          @unknown default:
            print("❌ Unknown purchase result")
            throw SubscriptionCore.StoreKitClientError.unknown
          }

        } catch let error as SubscriptionCore.StoreKitClientError {
          print("❌ StoreKit error: \(error)")
          throw error
        } catch {
          print("❌ Purchase failed with error: \(error)")
          print("❌ Error type: \(type(of: error))")
          throw SubscriptionCore.StoreKitClientError.storeKitError(error.localizedDescription)
        }
      },

      restorePurchases: {
        print("🔄 Restoring purchases...")

        do {
          // 同步最新的交易状态
          try await AppStore.sync()
          print("✅ Purchases restored successfully")
        } catch {
          print("❌ Failed to restore purchases: \(error)")
          throw SubscriptionCore.StoreKitClientError.storeKitError(error.localizedDescription)
        }
      },

      currentEntitlements: {
        var transactions: [Transaction] = []
        for await verification in Transaction.currentEntitlements {
          do {
            let transaction = try LiveStoreKitClient.checkVerified(verification)
            transactions.append(transaction)
          } catch {
            print("⚠️ Failed to verify transaction: \(error)")
          }
        }
        return transactions
      },

      transactionUpdates: {
        AsyncStream { continuation in
          Task {
            for await verification in Transaction.updates {
              do {
                let transaction = try LiveStoreKitClient.checkVerified(verification)
                continuation.yield(transaction)
              } catch {
                print("⚠️ Failed to verify transaction update: \(error)")
              }
            }
          }
        }
      },

      subscriptionStatus: { productID in
        do {
          guard let product = try await Product.products(for: [productID]).first else {
            return nil
          }
          return await product.subscription?.status.first?.state
        } catch {
          print("❌ Failed to get subscription status: \(error)")
          return nil
        }
      }
    )

  // MARK: - Helper Functions

  static func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
    switch result {
    case .unverified:
      throw SubscriptionCore.StoreKitClientError.verificationFailed
    case .verified(let safe):
      return safe
    }
  }
}

// MARK: - Dependency Registration

extension DependencyValues {
  public var liveStoreKitClient: SubscriptionCore.StoreKitClient {
    get {
      if #available(iOS 15.0, macOS 12.0, *) {
        return LiveStoreKitClient.live
      } else {
        return StoreKitClient.liveValue // 回退到模拟实现
      }
    }
    set { self[StoreKitClient.self] = newValue }
  }
}
