import AuthenticationClient
import ComposableArchitecture
import LoginCore
import SwiftUI
import UserStateSwiftUI
import CommonUI
#if canImport(UIKit)
import UIKit
#endif

public struct LoginView: View {
  @Perception.Bindable public var store: StoreOf<Login>
  @State private var isPrivacyPolicyAccepted = false

  public init(store: StoreOf<Login>) {
    self.store = store
  }

  public var body: some View {
    WithPerceptionTracking {
      VStack(spacing: 0) {
        Spacer()

        // 🌸 新拟物风格头部区域
        VStack(spacing: 32) {
          // App Icon - 新拟物设计
          ZStack {
            // 外圈新拟物效果
            Circle()
              .fill(
                LinearGradient(
                  colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
              .frame(width: 120, height: 120)
              .shadow(color: .shadowDark.opacity(0.2), radius: 16, x: 6, y: 6)
              .shadow(color: .shadowLight, radius: 16, x: -6, y: -6)

            // 内圈装饰
            Circle()
              .fill(
                LinearGradient(
                  colors: [.softPink.opacity(0.3), .warmOrange.opacity(0.3)],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
              .frame(width: 85, height: 85)

            // 主图标
            Image(systemName: "sparkles")
              .font(.system(size: 36, weight: .light))
              .foregroundStyle(
                LinearGradient(
                  colors: [.softPink, .warmOrange],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
          }

          // 欢迎文字 - 新拟物风格
          VStack(spacing: 12) {
            Text("欢迎来到婚纱助手")
              .font(.welcomeTitle)
              .fontWeight(.bold)
              .foregroundColor(.textPrimary)

            Text("使用Apple ID安全快速登录\n享受个性化婚纱设计服务")
              .font(.welcomeSubtitle)
              .foregroundColor(.textSecondary)
              .multilineTextAlignment(.center)
              .lineSpacing(4)
          }
        }

        Spacer()

        // 🌸 登录区域 - 新拟物卡片
        ContentCard(style: .soft) {
          VStack(spacing: 24) {
            // 隐私政策区域
            privacyPolicySection

            // Apple 登录按钮 - 只保留系统原生按钮
            if #available(iOS 13.0, macOS 10.15, *) {
              QuickAppleSignInButton(
                onSignIn: { credential in
                  print("🍎 LoginView: Apple ID登录凭证获取成功")
                  store.send(.appleSignIn(credential))
                },
                onError: { error in
                  print("🍎 LoginView: Apple ID登录失败: \(error.localizedDescription)")
                  store.send(.loginResponse(.failure(error)))
                }
              )
              .disabled(!isPrivacyPolicyAccepted)
              .opacity(isPrivacyPolicyAccepted ? 1.0 : 0.6)
              .animation(.easeInOut(duration: 0.2), value: isPrivacyPolicyAccepted)
              .cornerRadius(NeumorphicDesign.largeRadius)
              .shadow(
                color: .shadowDark.opacity(0.15),
                radius: NeumorphicDesign.mediumShadowRadius,
                x: 0,
                y: 4
              )
            }
          }
        }
        .padding(.horizontal, 24)

        Spacer()
      }
      .navigationTitle("登录")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .background(
        // 🌸 新拟物风格背景渐变
        LinearGradient(
          colors: [
            .creamBackground,
            .softBeige.opacity(0.8),
            .lightGray.opacity(0.6)
          ],
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
      )
    }
  }

  // MARK: - 🌸 新拟物风格隐私政策区域

  @ViewBuilder
  private var privacyPolicySection: some View {
    VStack(spacing: 16) {
      // 复选框与协议文字
      HStack(alignment: .top, spacing: 14) {
        // 新拟物风格复选框
        Button(action: {
          withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            isPrivacyPolicyAccepted.toggle()
          }
        }) {
          ZStack {
            // 复选框背景 - 新拟物效果
            RoundedRectangle(cornerRadius: 7)
              .fill(
                LinearGradient(
                  colors: isPrivacyPolicyAccepted 
                    ? [.softPink, .warmOrange]
                    : [.white.opacity(0.9), .softBeige.opacity(0.8)],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
              .frame(width: 24, height: 24)
              .shadow(
                color: isPrivacyPolicyAccepted 
                  ? .softPink.opacity(0.3) 
                  : .shadowDark.opacity(0.1),
                radius: isPrivacyPolicyAccepted ? 6 : 3,
                x: 0,
                y: 2
              )

            // 内阴影效果（未选中状态）
            if !isPrivacyPolicyAccepted {
              RoundedRectangle(cornerRadius: 7)
                .stroke(Color.shadowInner, lineWidth: 1)
                .blur(radius: 0.5)
                .frame(width: 24, height: 24)
            }

            // 勾选图标
            if isPrivacyPolicyAccepted {
              Image(systemName: "checkmark")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .scaleEffect(isPrivacyPolicyAccepted ? 1.0 : 0.5)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPrivacyPolicyAccepted)
            }
          }
        }
        .buttonStyle(PlainButtonStyle())

        // 协议文字
        VStack(alignment: .leading, spacing: 4) {
          HStack(alignment: .top, spacing: 0) {
            Text("我已阅读并同意")
              .font(.customFootnote)
              .foregroundColor(.textSecondary)

            Button("《隐私政策》") {
              openPrivacyPolicy()
            }
            .font(.customFootnote)
            .fontWeight(.medium)
            .foregroundColor(.softPink)
            .buttonStyle(PlainButtonStyle())

            Text("和")
              .font(.customFootnote)
              .foregroundColor(.textSecondary)

            Button("《用户协议》") {
              openUserAgreement()
            }
            .font(.customFootnote)
            .fontWeight(.medium)
            .foregroundColor(.softPink)
            .buttonStyle(PlainButtonStyle())
          }
        }

        Spacer()
      }

      // 安全提示 - 新拟物风格
      HStack(spacing: 10) {
        // 安全图标背景
        ZStack {
          Circle()
            .fill(
              LinearGradient(
                colors: [.successSoft.opacity(0.8), .successSoft.opacity(0.6)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 28, height: 28)
            .shadow(color: .successSoft.opacity(0.3), radius: 3, x: 0, y: 2)

          Image(systemName: "lock.shield.fill")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.white)
        }

        Text("您的隐私和数据安全是我们的首要关注")
          .font(.customCaption)
          .foregroundColor(.textSecondary)
          .multilineTextAlignment(.leading)

        Spacer()
      }
    }
  }

  // MARK: - Helper Methods

  private func openPrivacyPolicy() {
    print("📄 打开隐私政策")
    // TODO: 实现隐私政策页面导航
    // 可以使用 SafariView 或者应用内 WebView
    #if canImport(UIKit)
    if let url = URL(string: "https://example.com/privacy-policy") {
      UIApplication.shared.open(url)
    }
    #endif
  }

  private func openUserAgreement() {
    print("📄 打开用户协议")
    // TODO: 实现用户协议页面导航
    #if canImport(UIKit)
    if let url = URL(string: "https://example.com/user-agreement") {
      UIApplication.shared.open(url)
    }
    #endif
  }
}

#Preview {
  NavigationView {
    LoginView(
      store: Store(initialState: Login.State()) {
        Login()
      }
    )
  }
}
