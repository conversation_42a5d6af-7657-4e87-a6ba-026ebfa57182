import SwiftUI
import AuthenticationClient
import ComposableArchitecture
import UserStateCore

#if canImport(AuthenticationServices)
import AuthenticationServices
#endif

// MARK: - Apple Sign In Button

@available(iOS 13.0, macOS 10.15, *)
public struct AppleSignInButton: View {
  let onSignIn: (AppleIDCredential) -> Void
  let onError: (Error) -> Void
  
  public init(
    onSignIn: @escaping (AppleIDCredential) -> Void,
    onError: @escaping (Error) -> Void
  ) {
    self.onSignIn = onSignIn
    self.onError = onError
  }
  
  public var body: some View {
    #if canImport(AuthenticationServices)
    SignInWithAppleButton(
      onRequest: { request in
        print("🍎 配置Apple ID登录请求")
        request.requestedScopes = [.fullName, .email]
        // 添加更多配置以提高成功率
        request.nonce = UUID().uuidString
      },
      onCompletion: { result in
        print("🍎 Apple ID登录完成，结果: \(result)")
        switch result {
        case .success(let authorization):
          print("🍎 Apple ID授权成功")
          if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            print("🍎 获取到Apple ID凭证:")
            print("   用户ID: \(appleIDCredential.user)")
            print("   真实邮箱: \(appleIDCredential.email ?? "未提供")")
            print("   姓名: \(appleIDCredential.fullName?.formatted() ?? "未提供")")

            // 特别标注真实邮箱信息
            if let realEmail = appleIDCredential.email {
              print("✅ 获取到真实邮箱，将传递给后端: \(realEmail)")
            } else {
              print("⚠️ 未获取到真实邮箱，用户可能选择了隐藏邮箱")
            }

            let credential = AppleIDCredential(
              userID: appleIDCredential.user,
              email: appleIDCredential.email,
              fullName: appleIDCredential.fullName,
              identityToken: appleIDCredential.identityToken,
              authorizationCode: appleIDCredential.authorizationCode
            )
            onSignIn(credential)
          } else {
            print("❌ 无法获取Apple ID凭证")
            let error = NSError(
              domain: "AppleSignInError",
              code: -1,
              userInfo: [NSLocalizedDescriptionKey: "无法获取Apple ID凭证"]
            )
            onError(error)
          }
        case .failure(let error):
          print("❌ Apple ID登录失败: \(error)")
          // 处理特定的错误代码
          if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
              print("🍎 用户取消了Apple ID登录")
            case .failed:
              print("🍎 Apple ID登录失败")
            case .invalidResponse:
              print("🍎 Apple ID响应无效")
            case .notHandled:
              print("🍎 Apple ID登录未处理")
            case .unknown:
              print("🍎 Apple ID登录未知错误")
            case .notInteractive:
              print("🍎 Apple ID登录非交互式错误")
            case .matchedExcludedCredential:
              print("🍎 Apple ID凭证匹配排除项")
            case .credentialImport:
              print("🍎 Apple ID凭证导入错误")
            case .credentialExport:
              print("🍎 Apple ID凭证导出错误")
            @unknown default:
              print("🍎 Apple ID登录其他错误")
            }
          }
          onError(error)
        }
      }
    )
    .signInWithAppleButtonStyle(.black)
    .frame(height: 50)
    #else
    // Fallback for platforms that don't support Sign in with Apple
    Button("Sign in with Apple") {
      // Mock credential for non-Apple platforms
      let mockCredential = AppleIDCredential(
        userID: "mock_user_\(UUID().uuidString)",
        email: "<EMAIL>",
        fullName: PersonNameComponents(),
        identityToken: nil,
        authorizationCode: nil
      )
      onSignIn(mockCredential)
    }
    .font(.headline)
    .foregroundColor(.white)
    .frame(maxWidth: .infinity)
    .frame(height: 50)
    .background(Color.black)
    .cornerRadius(8)
    #endif
  }
}

// MARK: - Enhanced Login Prompt Modal

public struct EnhancedLoginPromptModal: View {
  let store: Store<UserState.State, UserState.Action>
  let onAppleSignIn: (AppleIDCredential) -> Void
  let onDismiss: () -> Void

  @State private var isLoading = false
  @State private var errorMessage: String?
  @State private var isPrivacyPolicyAccepted = false

  public init(
    store: StoreOf<UserState>,
    onAppleSignIn: @escaping (AppleIDCredential) -> Void,
    onDismiss: @escaping () -> Void
  ) {
    self.store = store
    self.onAppleSignIn = onAppleSignIn
    self.onDismiss = onDismiss
  }
  
  public var body: some View {
    WithPerceptionTracking {
      VStack(spacing: 24) {
        // Header
        VStack(spacing: 16) {
          Image(systemName: "crown.fill")
            .font(.system(size: 50))
            .foregroundStyle(
              LinearGradient(
                colors: [.pink, .purple],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
          
          VStack(spacing: 8) {
            Text("解锁完整体验")
              .font(.title2)
              .fontWeight(.bold)
            
            Text("选择您喜欢的登录方式")
              .font(.subheadline)
              .foregroundColor(.secondary)
              .multilineTextAlignment(.center)
          }
        }
        
        // Error Message
        WithPerceptionTracking {
          if let errorMessage = errorMessage ?? store.withState(\.error) {
            Text(errorMessage)
              .font(.caption)
              .foregroundColor(.red)
              .padding(.horizontal)
              .multilineTextAlignment(.center)
          }
        }
        
        // Privacy Policy Agreement
        privacyPolicySection

        // Sign In Options
        VStack(spacing: 16) {
          // Apple Sign In Button
          if #available(iOS 13.0, macOS 10.15, *) {
            WithPerceptionTracking {
              AppleSignInButton(
                onSignIn: { credential in
                  print("🍎 AppleSignInView: 开始处理Apple ID登录")
                  isLoading = true
                  errorMessage = nil
                  onAppleSignIn(credential)
                },
                onError: { error in
                  print("🍎 AppleSignInView: Apple ID登录出错")
                  isLoading = false

                  // 根据错误类型提供更友好的错误消息
                  if let authError = error as? ASAuthorizationError {
                    switch authError.code {
                    case .canceled:
                      errorMessage = "登录已取消"
                    case .failed:
                      errorMessage = "Apple登录失败，请检查网络连接后重试"
                    case .invalidResponse:
                      errorMessage = "Apple服务响应异常，请稍后重试"
                    case .notHandled:
                      errorMessage = "Apple登录服务暂时不可用"
                    case .unknown:
                      errorMessage = "Apple登录遇到未知错误，请重试"
                    case .notInteractive:
                      errorMessage = "Apple登录需要用户交互"
                    case .matchedExcludedCredential:
                      errorMessage = "Apple登录凭证不可用"
                    case .credentialImport:
                      errorMessage = "Apple登录凭证导入失败"
                    case .credentialExport:
                      errorMessage = "Apple登录凭证导出失败"
                    @unknown default:
                      errorMessage = "Apple登录失败，请重试"
                    }
                  } else {
                    errorMessage = "Apple登录失败，请重试"
                  }

                  print("🍎 Apple登录错误详情: \(error)")
                }
              )
              .disabled(isLoading || store.withState(\.isLoading) || !isPrivacyPolicyAccepted)
              .opacity((isLoading || store.withState(\.isLoading) || !isPrivacyPolicyAccepted) ? 0.6 : 1.0)
              .animation(.easeInOut(duration: 0.2), value: isPrivacyPolicyAccepted)
            }
          }
        }
        .padding(.horizontal)
        
        // Benefits Section (Optimized)
        VStack(alignment: .leading, spacing: 16) {
          Text("登录即可享受")
            .font(.system(size: 18, weight: .semibold))
            .foregroundColor(.primary)
            .padding(.horizontal)

          VStack(spacing: 12) {
            CompactBenefitRow(icon: "infinity", title: "无限创作", description: "不限次数生成专属婚纱照")
            CompactBenefitRow(icon: "cloud.fill", title: "云端保存", description: "作品永久保存，随时访问")
            CompactBenefitRow(icon: "sparkles", title: "专属特权", description: "高清下载、优先处理")
          }
          .padding(.horizontal)
        }
        
        // Dismiss Button
        Button("稍后再说") {
          onDismiss()
        }
        .font(.subheadline)
        .foregroundColor(.secondary)
        .disabled(isLoading)
        
        WithPerceptionTracking {
          if isLoading || store.withState(\.isLoading) {
            HStack(spacing: 8) {
              ProgressView()
                .scaleEffect(0.8)
              Text("登录中...")
                .font(.caption)
                .foregroundColor(.secondary)
            }
          }
        }
      }
      .padding(.vertical, 32)
      .background(Color.white)
      .cornerRadius(20)
      .shadow(radius: 20)
    }
  }

  // MARK: - Privacy Policy Section

  @ViewBuilder
  private var privacyPolicySection: some View {
    VStack(spacing: 12) {
      // Checkbox with agreement text
      HStack(alignment: .top, spacing: 12) {
        Button(action: {
          withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            isPrivacyPolicyAccepted.toggle()
          }
        }) {
          ZStack {
            RoundedRectangle(cornerRadius: 6)
              .stroke(
                isPrivacyPolicyAccepted ? Color.pink : Color.gray.opacity(0.5),
                lineWidth: 2
              )
              .frame(width: 20, height: 20)
              .background(
                RoundedRectangle(cornerRadius: 6)
                  .fill(isPrivacyPolicyAccepted ? Color.pink : Color.clear)
              )

            if isPrivacyPolicyAccepted {
              Image(systemName: "checkmark")
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(.white)
                .scaleEffect(isPrivacyPolicyAccepted ? 1.0 : 0.5)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPrivacyPolicyAccepted)
            }
          }
        }
        .buttonStyle(PlainButtonStyle())

        // Agreement text with clickable links
        VStack(alignment: .leading, spacing: 4) {
          HStack(alignment: .top, spacing: 0) {
            Text("我已阅读并同意")
              .font(.caption)
              .foregroundColor(.secondary)

            Button("《隐私政策》") {
              openPrivacyPolicy()
            }
            .font(.caption)
            .foregroundColor(.pink)
            .buttonStyle(PlainButtonStyle())

            Text("和")
              .font(.caption)
              .foregroundColor(.secondary)

            Button("《用户协议》") {
              openUserAgreement()
            }
            .font(.caption)
            .foregroundColor(.pink)
            .buttonStyle(PlainButtonStyle())
          }
        }

        Spacer()
      }
      .padding(.horizontal)

      // Security notice
      HStack(spacing: 6) {
        Image(systemName: "lock.shield")
          .font(.caption2)
          .foregroundColor(.green)

        Text("您的隐私和数据安全是我们的首要关注")
          .font(.caption2)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.leading)

        Spacer()
      }
      .padding(.horizontal)
    }
    .padding(.vertical, 8)
  }

  // MARK: - Helper Methods

  private func openPrivacyPolicy() {
    print("📄 打开隐私政策")
    // TODO: 实现隐私政策页面导航
    // 可以使用 SafariView 或者应用内 WebView
  }

  private func openUserAgreement() {
    print("📄 打开用户协议")
    // TODO: 实现用户协议页面导航
    // 可以使用 SafariView 或者应用内 WebView
  }
}

// MARK: - Compact Benefit Row

private struct CompactBenefitRow: View {
  let icon: String
  let title: String
  let description: String

  var body: some View {
    HStack(spacing: 16) {
      // Icon with background
      ZStack {
        Circle()
          .fill(
            LinearGradient(
              colors: [.pink.opacity(0.1), .purple.opacity(0.1)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 36, height: 36)

        Image(systemName: icon)
          .font(.system(size: 16, weight: .semibold))
          .foregroundStyle(
            LinearGradient(
              colors: [.pink, .purple],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
      }

      VStack(alignment: .leading, spacing: 4) {
        Text(title)
          .font(.system(size: 15, weight: .semibold))
          .foregroundColor(.primary)

        Text(description)
          .font(.system(size: 13))
          .foregroundColor(.secondary)
          .lineLimit(2)
      }

      Spacer()
    }
    .padding(.vertical, 4)
  }
}

// MARK: - Quick Apple Sign In Button

public struct QuickAppleSignInButton: View {
  let onSignIn: (AppleIDCredential) -> Void
  let onError: (Error) -> Void
  
  public init(
    onSignIn: @escaping (AppleIDCredential) -> Void,
    onError: @escaping (Error) -> Void
  ) {
    self.onSignIn = onSignIn
    self.onError = onError
  }
  
  public var body: some View {
    if #available(iOS 13.0, macOS 10.15, *) {
      AppleSignInButton(onSignIn: onSignIn, onError: onError)
        .frame(height: 44)
    } else {
      Button("Sign in with Apple") {
        let mockCredential = AppleIDCredential(
          userID: "mock_user_\(UUID().uuidString)",
          email: "<EMAIL>",
          fullName: PersonNameComponents(),
          identityToken: nil,
          authorizationCode: nil
        )
        onSignIn(mockCredential)
      }
      .font(.headline)
      .foregroundColor(.white)
      .frame(maxWidth: .infinity)
      .frame(height: 44)
      .background(Color.black)
      .cornerRadius(8)
    }
  }
}

// Preview removed due to Store complexity - use in app context