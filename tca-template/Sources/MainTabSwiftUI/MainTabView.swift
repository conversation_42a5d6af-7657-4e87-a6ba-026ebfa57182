import SwiftUI
import Composable<PERSON>rchitecture
import MainTabCore
import <PERSON>Upload<PERSON>wift<PERSON>
import ImageTypeSelectionSwiftUI
import <PERSON>GenerationSwiftUI
import <PERSON>ViewSwiftUI
import ProfileSwiftUI
import SubscriptionSwiftUI
import SubscriptionCore

// MARK: - Main Tab View

public struct MainTabView: View {
  let store: StoreOf<MainTab>
  
  public init(store: StoreOf<MainTab>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      TabView(selection: Binding(
        get: { store.selectedTab },
        set: { store.send(.tabSelected($0)) }
      )) {
        // Home Tab
        HomeFlowView(store: store.scope(state: \.homeFlow, action: \.homeFlow))
          .tabItem {
            Label(MainTab.Tab.home.title, systemImage: MainTab.Tab.home.iconName)
          }
          .tag(MainTab.Tab.home)
        
        // Settings Tab
        ProfileView(store: store.scope(state: \.profile, action: \.profile))
          .tabItem {
            Label(MainTab.Tab.settings.title, systemImage: MainTab.Tab.settings.iconName)
          }
          .tag(MainTab.Tab.settings)
      }
      .accentColor(.blue)
      .sheet(isPresented: Binding(
        get: { store.isShowingSubscription },
        set: { store.send(.setShowingSubscription($0)) }
      )) {
        // 使用真正的订阅页面
        if let subscriptionStore = store.scope(state: \.subscription, action: \.subscription) {
          NavigationView {
            SubscriptionView(store: subscriptionStore)
              #if os(iOS)
              .navigationBarTitleDisplayMode(.inline)
              #endif
              .toolbar {
                ToolbarItem(placement: {
                  #if os(iOS)
                  return .navigationBarTrailing
                  #else
                  return .automatic
                  #endif
                }()) {
                  Button("关闭") {
                    store.send(.hideSubscription)
                  }
                }
              }
          }
        } else {
          // 备用简化视图
          VStack(spacing: 20) {
            Text("订阅页面")
              .font(.title)
            Text("正在加载订阅选项...")
              .foregroundColor(.secondary)
            Button("关闭") {
              store.send(.hideSubscription)
            }
            .buttonStyle(.borderedProminent)
          }
          .padding()
        }
      }
    }
  }
}

// MARK: - Home Flow View

struct HomeFlowView: View {
  let store: StoreOf<HomeFlow>
  
  var body: some View {
    WithPerceptionTracking {
      NavigationView {
        Group {
          switch store.currentStep {
          case .photoUpload:
            PhotoUploadView(store: store.scope(state: \.photoUpload, action: \.photoUpload))
          
          case .imageTypeSelection:
            ImageTypeSelectionView(store: store.scope(state: \.imageTypeSelection, action: \.imageTypeSelection))
          
          case .imageGeneration:
            if let imageGenerationStore = store.scope(state: \.imageGeneration, action: \.imageGeneration) {
              ImageGenerationView(store: imageGenerationStore)
            } else {
              Text("Image generation not available")
            }
          
          case .imageView:
            let _ = print("🖼️ [HomeFlowView] Rendering imageView case")
            let _ = print("🖼️ [HomeFlowView] ImageView state exists: \(store.imageView != nil)")
            if let imageViewStore = store.scope(state: \.imageView, action: \.imageView) {
              let _ = print("🖼️ [HomeFlowView] ImageViewStore created successfully, rendering ImageViewView")
              ImageViewView(store: imageViewStore)
            } else {
              let _ = print("❌ [HomeFlowView] ImageView state is nil, showing fallback text")
              Text("Image view not available")
                .foregroundColor(.red)
                .font(.title2)
                .padding()
            }
          }
        }
        .toolbar {
          // 全局重置按钮
          ToolbarItem(placement: .automatic) {
            if store.currentStep != .photoUpload {
              Button("重新开始") {
                store.send(.resetToStart)
              }
              .font(.caption)
              .foregroundColor(.orange)
            }
          }
        }
      }
    }
  }
}

// MARK: - Previews

#Preview("Main Tab") {
  MainTabView(
    store: Store(initialState: MainTab.State()) {
      MainTab()
    }
  )
}

#Preview("Home Flow") {
  HomeFlowView(
    store: Store(initialState: HomeFlow.State()) {
      HomeFlow()
    }
  )
}