import SwiftUI
import ComposableArchitecture
import CommonUI
import ImageViewCore
import ImageGenerationCore
import PhotosUI

#if canImport(UIKit)
import UIKit
#endif

public struct ImageViewView: View {
  let store: StoreOf<ImageViewCore.ImageView>
  @State private var downloadedImage: Image?
  @State private var isLoading = false
  @State private var loadError: String?
  @State private var showingPhotoPicker = false
  @State private var photoPickerItem: PhotosPickerItem?
  @State private var showingImageSaver = false
  @State private var imageDataToSave: Data?

  public init(store: StoreOf<ImageViewCore.ImageView>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      ZStack {
        // 🌸 新拟物风格渐变背景（深色主题）
        LinearGradient(
          colors: [
            Color.black,
            Color.black.opacity(0.95),
            Color.textPrimary.opacity(0.1)
          ],
          startPoint: .top,
          endPoint: .bottom
        )
        .ignoresSafeArea()
        
        VStack(spacing: 0) {
          // Navigation header
          headerSection
          
          // Main image display
          imageSection
          
          // Bottom actions
          bottomActions
        }
      }
      #if os(iOS)
      .navigationBarHidden(true)
      #endif
      .onAppear {
        store.send(.onAppear)
        loadImageFromURL()
      }
      .alert("保存成功！", isPresented: .constant(store.showingSaveSuccess)) {
        Button("确定") {
          store.send(.dismissSaveSuccess)
        }
      } message: {
        Text("图片已成功保存到相册")
      }
      .background(
        // 隐藏的 ImageSaver，用于真实保存功能
        Group {
          if showingImageSaver, let imageData = imageDataToSave {
            #if canImport(UIKit)
            ImageSaver(imageData: imageData) { success, error in
              handleSaveResult(success: success, error: error)
            }
            .frame(width: 0, height: 0)
            .opacity(0)
            #endif
          }
        }
      )
    }
  }
  
  // MARK: - 🌸 新拟物风格头部区域
  
  private var headerSection: some View {
    HStack {
      // 返回按钮 - 新拟物风格
      Button(action: { store.send(.backToTypeSelection) }) {
        ZStack {
          Circle()
            .fill(
              LinearGradient(
                colors: [.white.opacity(0.15), .white.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 44, height: 44)
            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
          
          Image(systemName: "chevron.left")
            .font(.system(size: 18, weight: .medium))
            .foregroundColor(.white)
        }
      }
      
      Spacer()
      
      // 标题区域 - 新拟物风格
      VStack(spacing: 4) {
        Text("生成的婚纱照")
          .font(.customHeadline)
          .fontWeight(.semibold)
          .foregroundColor(.white)
        
        Text(store.generatedImage.templateName)
          .font(.customCaption)
          .foregroundColor(.white.opacity(0.8))
      }
      
      Spacer()
      
      // 信息按钮 - 新拟物风格
      Button(action: { store.send(.toggleImageDetails) }) {
        ZStack {
          Circle()
            .fill(
              LinearGradient(
                colors: [.white.opacity(0.15), .white.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 44, height: 44)
            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
          
          Image(systemName: "info.circle")
            .font(.system(size: 18, weight: .medium))
            .foregroundColor(.white)
        }
      }
    }
    .padding(NeumorphicDesign.largePadding)
    .background(
      LinearGradient(
        colors: [Color.black.opacity(0.6), Color.clear],
        startPoint: .top,
        endPoint: .bottom
      )
    )
  }
  
  private var imageSection: some View {
    ZStack {
      // 实际图片显示
      if let downloadedImage = downloadedImage {
        downloadedImage
          .resizable()
          .aspectRatio(contentMode: .fit)
          .cornerRadius(20)
          .scaleEffect(store.zoomScale)
          .gesture(
            MagnificationGesture()
              .onChanged { value in
                store.send(.zoomChanged(value))
              }
          )
      } else if isLoading {
        // 🌸 新拟物风格加载状态
        RoundedRectangle(cornerRadius: NeumorphicDesign.cardRadius)
          .fill(
            LinearGradient(
              colors: [.textPrimary.opacity(0.3), .textPrimary.opacity(0.1)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .aspectRatio(3/4, contentMode: .fit)
          .overlay(
            VStack(spacing: NeumorphicDesign.largeSpacing) {
              EnhancedLoadingView(message: "正在加载图片...")
                .scaleEffect(0.8)

              VStack(spacing: NeumorphicDesign.smallSpacing) {
                Text("生成完成")
                  .font(.customTitle)
                  .fontWeight(.bold)
                  .foregroundColor(.white)

                Text(store.generatedImage.templateName)
                  .font(.customBody)
                  .foregroundColor(.white.opacity(0.8))
                  .multilineTextAlignment(.center)
              }
            }
          )
          .shadow(color: .black.opacity(0.3), radius: 12, x: 0, y: 6)
      } else if let loadError = loadError {
        // 🌸 新拟物风格错误状态
        RoundedRectangle(cornerRadius: NeumorphicDesign.cardRadius)
          .fill(
            LinearGradient(
              colors: [.errorSoft.opacity(0.4), .errorSoft.opacity(0.2)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .aspectRatio(3/4, contentMode: .fit)
          .overlay(
            VStack(spacing: NeumorphicDesign.largeSpacing) {
              // 错误图标
              ZStack {
                Circle()
                  .fill(
                    LinearGradient(
                      colors: [.errorSoft.opacity(0.8), .errorSoft.opacity(0.6)],
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    )
                  )
                  .frame(width: 80, height: 80)
                  .shadow(color: .errorSoft.opacity(0.3), radius: 8, x: 0, y: 4)

                Image(systemName: "exclamationmark.triangle.fill")
                  .font(.system(size: 32))
                  .foregroundColor(.white)
              }

              VStack(spacing: NeumorphicDesign.smallSpacing) {
                Text("加载失败")
                  .font(.customTitle)
                  .fontWeight(.bold)
                  .foregroundColor(.white)

                Text(loadError)
                  .font(.customBody)
                  .foregroundColor(.white.opacity(0.8))
                  .multilineTextAlignment(.center)
                  .padding(.horizontal)
              }

              PrimaryButton(
                title: "重试",
                style: .primary,
                action: { loadImageFromURL() }
              )
              .frame(width: 120)
            }
          )
          .shadow(color: .black.opacity(0.3), radius: 12, x: 0, y: 6)
      } else {
        // 🌸 新拟物风格默认占位符
        RoundedRectangle(cornerRadius: NeumorphicDesign.cardRadius)
          .fill(
            LinearGradient(
              colors: [.softPink, .warmOrange, .lightPurple],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .aspectRatio(3/4, contentMode: .fit)
          .overlay(
            VStack(spacing: NeumorphicDesign.largeSpacing) {
              // 占位图标
              ZStack {
                Circle()
                  .fill(Color.white.opacity(0.2))
                  .frame(width: 100, height: 100)
                  .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)

                Image(systemName: "photo.fill")
                  .font(.system(size: 40))
                  .foregroundColor(.white.opacity(0.9))
              }

              VStack(spacing: NeumorphicDesign.smallSpacing) {
                Text("生成的图片")
                  .font(.customTitle)
                  .fontWeight(.bold)
                  .foregroundColor(.white)

                Text(store.generatedImage.templateName)
                  .font(.customBody)
                  .foregroundColor(.white.opacity(0.8))
                  .multilineTextAlignment(.center)
              }
            }
          )
          .shadow(color: .black.opacity(0.3), radius: 12, x: 0, y: 6)
      }
      
      // 🌸 新拟物风格图片详情覆盖层
      if store.showingImageDetails {
        VStack {
          Spacer()
          
          ContentCard(style: .soft) {
            VStack(alignment: .leading, spacing: NeumorphicDesign.mediumSpacing) {
              HStack {
                Text("图片详情")
                  .font(.cardTitle)
                  .fontWeight(.bold)
                  .foregroundColor(.textPrimary)
                
                Spacer()
                
                Button(action: { store.send(.toggleImageDetails) }) {
                  Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.textTertiary)
                }
              }
              
              VStack(spacing: NeumorphicDesign.smallSpacing) {
                detailRow("模板名称", store.generatedImage.templateName)
                detailRow("生成时间", formatDate(store.generatedImage.generatedAt))
                detailRow("处理时长", "\(Int(store.generatedImage.processingTime))秒")
                detailRow("图片ID", String(store.generatedImage.id.prefix(8)))
              }
            }
          }
          .padding(NeumorphicDesign.largePadding)
        }
      }
    }
    .padding(NeumorphicDesign.largePadding)
  }
  
  // MARK: - 🌸 新拟物风格底部操作区域
  
  private var bottomActions: some View {
    VStack(spacing: NeumorphicDesign.mediumSpacing) {
      // 错误消息显示
      if let saveError = store.saveError {
        ContentCard(style: .flat) {
          HStack(spacing: NeumorphicDesign.smallSpacing) {
            Image(systemName: "exclamationmark.triangle.fill")
              .foregroundColor(.errorSoft)
            
            Text("保存失败：\(saveError)")
              .font(.customCaption)
              .foregroundColor(.textSecondary)
            
            Spacer()
          }
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
      }
      
      if let shareError = store.shareError {
        ContentCard(style: .flat) {
          HStack(spacing: NeumorphicDesign.smallSpacing) {
            Image(systemName: "exclamationmark.triangle.fill")
              .foregroundColor(.errorSoft)
            
            Text("分享失败：\(shareError)")
              .font(.customCaption)
              .foregroundColor(.textSecondary)
            
            Spacer()
          }
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
      }

      // 操作按钮区域
      ContentCard(style: .soft) {
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          // 保存按钮
          PrimaryButton(
            title: store.isSaving ? "保存中..." : "保存到相册",
            style: .primary,
            isLoading: store.isSaving,
            isDisabled: store.isSaving
          ) {
            saveImageToPhotos()
          }
          
          // 重新开始按钮
          PrimaryButton(
            title: "重新开始",
            style: .secondary,
            action: {
              print("🔄 [ImageView] User clicked restart button")
              store.send(.backToTypeSelection)
            }
          )
        }
      }
      .padding(.horizontal, NeumorphicDesign.largePadding)
    }
    .padding(.bottom, NeumorphicDesign.largePadding)
    .background(
      LinearGradient(
        colors: [Color.clear, Color.black.opacity(0.8)],
        startPoint: .top,
        endPoint: .bottom
      )
    )
  }
  
  // MARK: - 🌸 新拟物风格详情行组件
  
  private func detailRow(_ label: String, _ value: String) -> some View {
    HStack {
      Text(label)
        .font(.customCaption)
        .foregroundColor(.textSecondary)
      
      Spacer()
      
      Text(value)
        .font(.customCaption)
        .fontWeight(.medium)
        .foregroundColor(.textPrimary)
    }
    .padding(.vertical, 2)
  }
  
  private func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .short
    return formatter.string(from: date)
  }

  // MARK: - SwiftUI 保存功能

  private func saveImageToPhotos() {
    print("🖼️ [ImageView] Starting real save process")

    // 首先检查是否有图片数据
    let imageData = store.generatedImage.imageData

    if !imageData.isEmpty {
      // 如果有图片数据，直接保存
      print("✅ [ImageView] Using existing image data: \(imageData.count) bytes")
      saveImageWithRealSaver(imageData)
    } else if let imageUrl = store.generatedImage.imageUrl {
      // 如果没有图片数据，先下载
      print("🔄 [ImageView] No image data, downloading from URL: \(imageUrl)")
      store.send(.saveImage) // 触发下载

      // 监听下载完成
      Task {
        await waitForImageDataAndSave()
      }
    } else {
      // 既没有数据也没有URL
      print("❌ [ImageView] No image data or URL available")
      store.send(.saveFailed("图片数据无效，无法保存"))
    }
  }

  private func saveImageWithRealSaver(_ imageData: Data) {
    print("📱 [ImageView] Using real ImageSaver to save image")
    print("📊 [ImageView] Image data size: \(imageData.count) bytes")

    // 设置要保存的图片数据
    self.imageDataToSave = imageData

    // 显示 ImageSaver（触发保存）
    withAnimation {
      self.showingImageSaver = true
    }
  }

  @MainActor
  private func handleSaveResult(success: Bool, error: (any Error)?) {
    // 隐藏 ImageSaver
    withAnimation {
      self.showingImageSaver = false
    }

    // 清理数据
    self.imageDataToSave = nil

    // 处理结果
    if success {
      print("✅ [ImageView] Image saved successfully")
      store.send(.saveCompleted)
    } else {
      let errorMessage = error?.localizedDescription ?? "保存失败"
      print("❌ [ImageView] Save failed: \(errorMessage)")
      store.send(.saveFailed(errorMessage))
    }
  }

  @MainActor
  private func waitForImageDataAndSave() async {
    // 等待图片数据下载完成
    var attempts = 0
    let maxAttempts = 50 // 最多等待5秒

    while attempts < maxAttempts {
      let currentImageData = store.generatedImage.imageData

      if !currentImageData.isEmpty {
        print("✅ [ImageView] Image data downloaded, proceeding with save")
        await MainActor.run {
          saveImageWithRealSaver(currentImageData)
        }
        return
      }

      // 等待100ms后重试
      try? await Task.sleep(nanoseconds: 100_000_000)
      attempts += 1
    }

    // 超时
    print("❌ [ImageView] Timeout waiting for image data")
    await MainActor.run {
      store.send(.saveFailed("下载图片超时，请重试"))
    }
  }

  // 旧的模拟保存方法已移除，现在使用真实的 ImageSaver

  private func loadImageFromURL() {
    guard let urlString = store.generatedImage.imageUrl,
          let url = URL(string: urlString),
          downloadedImage == nil else {
      print("⚠️ [ImageView] No image URL available or image already loaded")
      return
    }

    print("🔄 [ImageView] Loading image from URL: \(urlString)")
    isLoading = true
    loadError = nil

    Task {
      do {
        let (data, _) = try await URLSession.shared.data(from: url)
        await MainActor.run {
          #if canImport(UIKit)
          if let uiImage = UIKit.UIImage(data: data) {
            self.downloadedImage = Image(uiImage: uiImage)
            print("✅ [ImageView] Image loaded successfully from: \(urlString)")
          } else {
            self.loadError = "Failed to create image from data"
            print("❌ [ImageView] Failed to create UIImage from data")
          }
          #else
          // For other platforms, just mark as loaded
          self.downloadedImage = Image(systemName: "photo.fill")
          print("✅ [ImageView] Image placeholder loaded for: \(urlString)")
          #endif
          self.isLoading = false
        }
      } catch {
        await MainActor.run {
          self.loadError = error.localizedDescription
          self.isLoading = false
          print("❌ [ImageView] Failed to load image: \(error)")
        }
      }
    }
  }
}

// MARK: - 真实的图片保存功能

#if canImport(UIKit)
struct ImageSaver: UIViewControllerRepresentable {
  let imageData: Data
  let onSaveCompleted: @MainActor (Bool, Error?) -> Void

  func makeUIViewController(context: Context) -> UIViewController {
    let viewController = UIViewController()

    // 在主线程上执行保存操作
    DispatchQueue.main.async {
      self.saveImageToPhotos(viewController: viewController, context: context)
    }

    return viewController
  }

  func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
    // 不需要更新
  }

  private func saveImageToPhotos(viewController: UIViewController, context: Context) {
    guard let image = UIImage(data: imageData) else {
      onSaveCompleted(false, ImageSaveError.invalidImageData)
      return
    }

    // 使用 UIImageWriteToSavedPhotosAlbum 保存图片
    UIImageWriteToSavedPhotosAlbum(
      image,
      context.coordinator,
      #selector(Coordinator.saveCompleted(_:didFinishSavingWithError:contextInfo:)),
      nil
    )
  }

  func makeCoordinator() -> Coordinator {
    Coordinator(onSaveCompleted: onSaveCompleted)
  }

  class Coordinator: NSObject {
    let onSaveCompleted: @MainActor (Bool, Error?) -> Void

    init(onSaveCompleted: @escaping @MainActor (Bool, Error?) -> Void) {
      self.onSaveCompleted = onSaveCompleted
    }

    @objc func saveCompleted(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
      let completion = self.onSaveCompleted
      Task { @MainActor in
        if let error = error {
          print("❌ [ImageSaver] Failed to save image: \(error.localizedDescription)")
          completion(false, error)
        } else {
          print("✅ [ImageSaver] Image saved to photo library successfully")
          completion(true, nil)
        }
      }
    }
  }
}
#endif

#Preview {
  ImageViewView(
    store: Store(
      initialState: ImageViewCore.ImageView.State(
        generatedImage: GeneratedImage(
          id: "test-id",
          templateId: "romantic-wedding",
          templateName: "Romantic Wedding",
          imageData: Data(),
          generatedAt: Date(),
          processingTime: 35.0
        )
      )
    ) {
      ImageViewCore.ImageView()
    }
  )
}