import Foundation
import ComposableArchitecture
import NetworkClient
import Dependencies
import AuthenticationClient
import CommonUI

// MARK: - New Image Generation Client Interface

@DependencyClient
public struct NewImageGenerationClient: Sendable {
  /// Generate image with uploaded image URLs
  public var generateImageSync: @Sendable (NewGenerateImageRequest) async throws -> NewGeneratedImageResponse = { _ in
    throw NewImageGenerationError.notImplemented
  }

  /// Check generation status by task ID
  public var getGenerationRecord: @Sendable (String) async throws -> NewTaskStatusResponse = { _ in
    throw NewImageGenerationError.notImplemented
  }
}

// MARK: - Request/Response Models

public struct NewGenerateImageRequest: Codable, Sendable {
  public let prompt: String
  public let size: String
  public let isEnhance: Bool
  public let uploadCn: Bool
  public let nVariants: Int
  public let enableFallback: Bool
  public let fallbackModel: String
  public let filesUrl: [String]
  public let callBackUrl: String

  public init(
    prompt: String,
    size: String = "1:1",
    isEnhance: Bool = false,
    uploadCn: Bool = false,
    nVariants: Int = 1,
    enableFallback: Bool = false,
    fallbackModel: String = "FLUX_MAX",
    filesUrl: [String],
    callBackUrl: String = "http://c89de6f7.natappfree.cc/api/v1/image/callback"
  ) {
    self.prompt = prompt
    self.size = size
    self.isEnhance = isEnhance
    self.uploadCn = uploadCn
    self.nVariants = nVariants
    self.enableFallback = enableFallback
    self.fallbackModel = fallbackModel
    self.filesUrl = filesUrl
    self.callBackUrl = callBackUrl
  }
}

public struct NewGeneratedImageResponse: Codable, Sendable {
  public let code: Int
  public let message: String
  public let data: GenerationData?

  public init(
    code: Int,
    message: String,
    data: GenerationData? = nil
  ) {
    self.code = code
    self.message = message
    self.data = data
  }

  // 便利属性，判断是否成功
  public var isSuccess: Bool {
    return code == 0
  }
}

public struct GenerationData: Codable, Sendable {
  public let taskId: String?
  public let generationRecordId: String?
  public let status: String?
  public let estimatedCompletionTime: String?
  public let callbackUrl: String?
  public let remainingCredits: Int
  public let remainingMonthlyUsage: Int
  public let subscriptionStatus: String?
  public let trialMessage: String?
  public let isTrialUser: Bool?
  public let apiResponse: String?

  private enum CodingKeys: String, CodingKey {
    case taskId = "task_id"
    case generationRecordId = "generation_record_id"
    case status
    case estimatedCompletionTime = "estimated_completion_time"
    case callbackUrl = "callback_url"
    case remainingCredits = "remaining_credits"
    case remainingMonthlyUsage = "remaining_monthly_usage"
    case subscriptionStatus = "subscription_status"
    case trialMessage = "trial_message"
    case isTrialUser = "is_trial_user"
    case apiResponse = "api_response"
  }

  public init(
    taskId: String? = nil,
    generationRecordId: String? = nil,
    status: String? = nil,
    estimatedCompletionTime: String? = nil,
    callbackUrl: String? = nil,
    remainingCredits: Int = 0,
    remainingMonthlyUsage: Int = 0,
    subscriptionStatus: String? = nil,
    trialMessage: String? = nil,
    isTrialUser: Bool? = nil,
    apiResponse: String? = nil
  ) {
    self.taskId = taskId
    self.generationRecordId = generationRecordId
    self.status = status
    self.estimatedCompletionTime = estimatedCompletionTime
    self.callbackUrl = callbackUrl
    self.remainingCredits = remainingCredits
    self.remainingMonthlyUsage = remainingMonthlyUsage
    self.subscriptionStatus = subscriptionStatus
    self.trialMessage = trialMessage
    self.isTrialUser = isTrialUser
    self.apiResponse = apiResponse
  }
}

// MARK: - Task Status Response Models

public struct NewTaskStatusResponse: Codable, Sendable {
  public let code: Int
  public let message: String
  public let data: GenerationRecordData?

  public init(
    code: Int,
    message: String,
    data: GenerationRecordData? = nil
  ) {
    self.code = code
    self.message = message
    self.data = data
  }

  // 便利属性，判断是否成功
  public var isSuccess: Bool {
    return code == 0
  }
}

// MARK: - Record Info Response Models (for polling)

public struct RecordInfoResponse: Codable, Sendable {
  public let code: Int
  public let msg: String
  public let data: GenerationRecordData?

  private enum CodingKeys: String, CodingKey {
    case code
    case msg
    case data
  }

  public init(
    code: Int,
    msg: String,
    data: GenerationRecordData? = nil
  ) {
    self.code = code
    self.msg = msg
    self.data = data
  }

  // 便利属性，判断是否成功
  public var isSuccess: Bool {
    return code == 200
  }

  public var success: Bool {
    return code == 200
  }

  public var message: String {
    return msg
  }

  public var error: String? {
    return code != 200 ? msg : nil
  }
}



public struct GenerationRecordData: Codable, Sendable {
  public let taskId: String
  public let paramJson: String
  public let completeTime: Int64?
  public let response: GenerationResponse?  // 修改为可选，因为GENERATING状态时为null
  public let successFlag: Int
  public let status: String  // SUCCESS, GENERATE_FAILED, etc.
  public let errorCode: Int?
  public let errorMessage: String?
  public let createTime: Int64
  public let progress: String

  private enum CodingKeys: String, CodingKey {
    case taskId
    case paramJson
    case completeTime
    case response
    case successFlag
    case status
    case errorCode
    case errorMessage
    case createTime
    case progress
  }

  public init(
    taskId: String,
    paramJson: String,
    completeTime: Int64? = nil,
    response: GenerationResponse? = nil,
    successFlag: Int,
    status: String,
    errorCode: Int? = nil,
    errorMessage: String? = nil,
    createTime: Int64,
    progress: String
  ) {
    self.taskId = taskId
    self.paramJson = paramJson
    self.completeTime = completeTime
    self.response = response
    self.successFlag = successFlag
    self.status = status
    self.errorCode = errorCode
    self.errorMessage = errorMessage
    self.createTime = createTime
    self.progress = progress
  }

  // 便利属性
  public var isSuccess: Bool {
    return status == "SUCCESS"
  }

  public var progressValue: Double {
    return Double(progress) ?? 0.0
  }

  public var imageUrls: [String] {
    return response?.resultUrls ?? []
  }
}

public struct GenerationResponse: Codable, Sendable {
  public let resultUrls: [String]?

  public init(resultUrls: [String]? = nil) {
    self.resultUrls = resultUrls
  }
}

public struct TaskStatusData: Codable, Sendable {
  public let recordId: String
  public let taskId: String?
  public let status: String
  public let progress: Double?
  public let imageUrls: [String]
  public let thumbnailUrls: [String]?
  public let estimatedTimeRemaining: Int?
  public let error: String?
  public let createdAt: String?
  public let updatedAt: String?
  public let prompt: String?
  public let originalImageUrls: [String]?

  private enum CodingKeys: String, CodingKey {
    case recordId = "record_id"
    case taskId = "task_id"
    case status
    case progress
    case imageUrls = "image_urls"
    case thumbnailUrls = "thumbnail_urls"
    case estimatedTimeRemaining = "estimated_time_remaining"
    case error
    case createdAt = "created_at"
    case updatedAt = "updated_at"
    case prompt
    case originalImageUrls = "original_image_urls"
  }

  public init(
    recordId: String,
    taskId: String? = nil,
    status: String,
    progress: Double? = nil,
    imageUrls: [String] = [],
    thumbnailUrls: [String]? = nil,
    estimatedTimeRemaining: Int? = nil,
    error: String? = nil,
    createdAt: String? = nil,
    updatedAt: String? = nil,
    prompt: String? = nil,
    originalImageUrls: [String]? = nil
  ) {
    self.recordId = recordId
    self.taskId = taskId
    self.status = status
    self.progress = progress
    self.imageUrls = imageUrls
    self.thumbnailUrls = thumbnailUrls
    self.estimatedTimeRemaining = estimatedTimeRemaining
    self.error = error
    self.createdAt = createdAt
    self.updatedAt = updatedAt
    self.prompt = prompt
    self.originalImageUrls = originalImageUrls
  }
}

// MARK: - Error Types

public enum NewImageGenerationError: Error, Equatable, LocalizedError, Sendable {
  case notImplemented
  case networkError(String)
  case invalidRequest(String)
  case generationFailed(String)
  case serverError(String)
  case authenticationRequired
  case apiError(Int, String) // 新增：API错误，包含错误码和消息
  
  public var errorDescription: String? {
    switch self {
    case .notImplemented:
      return "Generation functionality not implemented"
    case .networkError(let message):
      return "Network error: \(message)"
    case .invalidRequest(let message):
      return "Invalid request: \(message)"
    case .generationFailed(let message):
      return "Generation failed: \(message)"
    case .serverError(let message):
      return "Server error: \(message)"
    case .authenticationRequired:
      return "Authentication required for generation"
    case .apiError(let code, let message):
      return "API error (\(code)): \(message)"
    }
  }
}

// MARK: - Error Codes

public enum GenerationErrorCode: Int, CaseIterable {
  case success = 0                    // 成功
  case generalError = 1               // 通用错误
  case insufficientCredits = 1001     // 积分不足
  case noSubscription = 1002          // 无有效订阅
  case monthlyLimitReached = 1003     // 月度限制已达
  case permissionDenied = 1004        // 权限被拒绝
  case invalidParameters = 2001       // 参数错误
  case invalidPrompt = 2002           // 提示词无效
  case invalidSize = 2003             // 尺寸无效
  case invalidVariants = 2004         // 变体数量无效
  case apiTokenError = 3001           // API令牌错误
  case apiAuthFailed = 3002           // API认证失败
  case apiRequestFailed = 3003        // API请求失败
  case serviceUnavailable = 3004      // 服务不可用
  case serviceMaintenance = 3005      // 服务维护中
  case queueFull = 3006              // 队列已满
  case internalError = 5001           // 内部错误

  public var userMessage: String {
    switch self {
    case .success:
      return "操作成功"
    case .generalError:
      return "操作失败，请稍后重试"
    case .insufficientCredits:
      return "积分不足，请充值后再试"
    case .noSubscription:
      return "您还没有有效的订阅，请先订阅服务"
    case .monthlyLimitReached:
      return "本月免费生图次数已用完\n\n💎 立即充值解锁更多次数\n🎯 或升级会员享受无限生图\n\n让我们继续为您创造美丽瞬间！"
    case .permissionDenied:
      return "权限不足，请联系客服"
    case .invalidParameters:
      return "参数错误，请检查输入"
    case .invalidPrompt:
      return "提示词格式不正确，请重新输入"
    case .invalidSize:
      return "图片尺寸设置错误"
    case .invalidVariants:
      return "生成数量设置错误"
    case .apiTokenError:
      return "认证令牌错误，请重新登录"
    case .apiAuthFailed:
      return "认证失败，请重新登录"
    case .apiRequestFailed:
      return "请求失败，请检查网络连接"
    case .serviceUnavailable:
      return "服务暂时不可用，请稍后重试"
    case .serviceMaintenance:
      return "服务正在维护中，请稍后重试"
    case .queueFull:
      return "当前请求较多，请稍后重试"
    case .internalError:
      return "系统内部错误，请联系客服"
    }
  }
}

// MARK: - Dependency Registration

extension NewImageGenerationClient: DependencyKey {
  public static let liveValue = Self.live()
}

// MARK: - Live Implementation Factory

extension NewImageGenerationClient {
  public static func live() -> Self {
    return Self(
      generateImageSync: { request in
        return try await generateImageSyncImpl(request)
      },
      getGenerationRecord: { taskId in
        return try await getGenerationRecordImpl(taskId)
      }
    )
  }
}

// MARK: - Implementation Functions

private func generateImageSyncImpl(_ request: NewGenerateImageRequest) async throws -> NewGeneratedImageResponse {
  print("🎨 [NewImageGeneration] Live implementation - Starting image generation")
  print("🎨 [NewImageGeneration] Live implementation - Request parameters:")
  print("   - Files URLs: \(request.filesUrl)")
  print("   - Prompt: \(request.prompt)")
  print("   - Size: \(request.size)")
  print("   - Variants: \(request.nVariants)")
  print("   - Is Enhance: \(request.isEnhance)")
  print("   - Upload CN: \(request.uploadCn)")
  print("   - Enable Fallback: \(request.enableFallback)")
  print("   - Fallback Model: \(request.fallbackModel)")
  print("   - Callback URL: \(request.callBackUrl)")

  // Get authentication token
  guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
    print("❌ [NewImageGeneration] No valid authentication token found")
    throw NewImageGenerationError.authenticationRequired
  }

  print("🔐 [NewImageGeneration] Using auth token: \(authToken.prefix(50))...")

  // Create network request
  let baseURL = APIEndpoints.baseURL
  let url = URL(string: "\(baseURL)/api/v1/image/generate-image")!

  // Encode request body
  let encoder = JSONEncoder()
  let requestData = try encoder.encode(request)

  print("🎨 [NewImageGeneration] Live implementation - Request body JSON:")
  if let jsonString = String(data: requestData, encoding: .utf8) {
    print(jsonString)
  }

  // Use URLSession directly
  do {
    // Create URLRequest
    var urlRequest = URLRequest(url: url)
    urlRequest.httpMethod = "POST"
    urlRequest.timeoutInterval = 120.0 // Longer timeout for image generation

    // Set headers
    urlRequest.setValue(authToken, forHTTPHeaderField: "Authorization")
    urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
    urlRequest.setValue("BridalApp/1.0.0", forHTTPHeaderField: "User-Agent")
    urlRequest.setValue("*/*", forHTTPHeaderField: "Accept")
    urlRequest.setValue("keep-alive", forHTTPHeaderField: "Connection")

    // Set body
    urlRequest.httpBody = requestData

    print("🎨 [NewImageGeneration] Live implementation - Sending request to: \(url.absoluteString)")
    print("🎨 [NewImageGeneration] Live implementation - Request headers: \(urlRequest.allHTTPHeaderFields ?? [:])")
  print("🎨 [NewImageGeneration] Live implementation - Request body: \(String(data: requestData, encoding: .utf8) ?? "Unable to decode")")

    print("🌐 [NewImageGeneration] Live implementation - Making URLSession request...")
    let (responseData, response) = try await URLSession.shared.data(for: urlRequest)

    print("🌐 [NewImageGeneration] Live implementation - Received response:")
    if let httpResponse = response as? HTTPURLResponse {
      print("   - Status code: \(httpResponse.statusCode)")
      print("   - Headers: \(httpResponse.allHeaderFields)")
    }
    print("   - Data size: \(responseData.count) bytes")

    // Log raw response for debugging
    if let responseString = String(data: responseData, encoding: .utf8) {
      print("✅ [NewImageGeneration] Live implementation - Raw response:")
      print(responseString)
    }

    // Parse response first (even for HTTP error status codes)
    let decoder = JSONDecoder()
    let generationResponse: NewGeneratedImageResponse

    do {
      generationResponse = try decoder.decode(NewGeneratedImageResponse.self, from: responseData)
    } catch {
      // If JSON parsing fails, check HTTP status code
      if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode >= 400 {
        throw NewImageGenerationError.serverError("HTTP \(httpResponse.statusCode)")
      }
      throw NewImageGenerationError.serverError("Failed to parse response: \(error.localizedDescription)")
    }

    print("✅ [NewImageGeneration] Live implementation - Response parsed successfully!")
    print("✅ [NewImageGeneration] Live implementation - Response code: \(generationResponse.code)")
    print("✅ [NewImageGeneration] Live implementation - Response message: \(generationResponse.message)")
    print("✅ [NewImageGeneration] Live implementation - Is success: \(generationResponse.isSuccess)")

    if let data = generationResponse.data {
      print("✅ [NewImageGeneration] Live implementation - Generation data:")
      if let taskId = data.taskId {
        print("   - Task ID: \(taskId)")
      }
      if let generationRecordId = data.generationRecordId {
        print("   - Generation Record ID: \(generationRecordId)")
      }
      if let status = data.status {
        print("   - Status: \(status)")
      }
      if let estimatedCompletionTime = data.estimatedCompletionTime {
        print("   - Estimated Completion Time: \(estimatedCompletionTime)")
      }
      print("   - Remaining Credits: \(data.remainingCredits)")
      print("   - Remaining Monthly Usage: \(data.remainingMonthlyUsage)")
      if let isTrialUser = data.isTrialUser {
        print("   - Is Trial User: \(isTrialUser)")
      }
      if let subscriptionStatus = data.subscriptionStatus {
        print("   - Subscription Status: \(subscriptionStatus)")
      }
    }

    // 检查业务逻辑是否成功（基于 code 字段，而不是 HTTP 状态码）
    if !generationResponse.isSuccess {
      print("❌ [NewImageGeneration] Live implementation - Generation failed with code: \(generationResponse.code)")

      // 根据错误码创建相应的错误
      if let errorCode = GenerationErrorCode(rawValue: generationResponse.code) {
        print("❌ [NewImageGeneration] Live implementation - Error type: \(errorCode)")
        print("❌ [NewImageGeneration] Live implementation - User message: \(errorCode.userMessage)")
        throw NewImageGenerationError.apiError(generationResponse.code, errorCode.userMessage)
      } else {
        // 未知错误码
        print("❌ [NewImageGeneration] Live implementation - Unknown error code: \(generationResponse.code)")
        throw NewImageGenerationError.apiError(generationResponse.code, generationResponse.message)
      }
    } else {
      print("✅ [NewImageGeneration] Live implementation - Generation task submitted successfully!")
    }

    return generationResponse

  } catch {
    print("❌ [NewImageGeneration] Live implementation - Generation failed with error: \(error)")

    if error is DecodingError {
      throw NewImageGenerationError.serverError("Failed to parse server response")
    } else {
      throw NewImageGenerationError.generationFailed(error.localizedDescription)
    }
  }
}

// MARK: - Generation Record Implementation

private func getGenerationRecordImpl(_ taskId: String) async throws -> NewTaskStatusResponse {
  print("🔍 [NewImageGeneration] Live implementation - Checking generation record")
  print("🔍 [NewImageGeneration] Live implementation - Task ID: \(taskId)")

  // Get authentication token
  guard let authToken = AccessTokenManager.getAuthorizationHeader() else {
    print("❌ [NewImageGeneration] No valid authentication token found")
    throw NewImageGenerationError.authenticationRequired
  }

  print("🔐 [NewImageGeneration] Using auth token: \(authToken.prefix(50))...")

  // Create network request
  let baseURL = APIEndpoints.baseURL
  let url = URL(string: "\(baseURL)/api/v1/image/record-info/\(taskId)")!

  // Create URL request
  var urlRequest = URLRequest(url: url)
  urlRequest.httpMethod = "GET"
  urlRequest.setValue(authToken, forHTTPHeaderField: "Authorization")
  urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
  urlRequest.setValue("BridalApp/1.0.0", forHTTPHeaderField: "User-Agent")
  urlRequest.setValue("*/*", forHTTPHeaderField: "Accept")
  urlRequest.setValue("keep-alive", forHTTPHeaderField: "Connection")
  urlRequest.timeoutInterval = 30.0

  print("🔍 [NewImageGeneration] Live implementation - Sending request to: \(url.absoluteString)")
  print("🔍 [NewImageGeneration] Live implementation - Request headers: \(urlRequest.allHTTPHeaderFields ?? [:])")

  print("🌐 [NewImageGeneration] Live implementation - Making URLSession request...")
  let (responseData, response) = try await URLSession.shared.data(for: urlRequest)

  print("🌐 [NewImageGeneration] Live implementation - Received response:")
  if let httpResponse = response as? HTTPURLResponse {
    print("   - Status code: \(httpResponse.statusCode)")
    print("   - Headers: \(httpResponse.allHeaderFields)")
  }
  print("   - Data size: \(responseData.count) bytes")

  // Log raw response for debugging
  if let responseString = String(data: responseData, encoding: .utf8) {
    print("✅ [NewImageGeneration] Live implementation - Raw response:")
    print(responseString)
  }

  // Parse response first (even for HTTP error status codes)
  let decoder = JSONDecoder()
  let recordInfoResponse: RecordInfoResponse

  do {
    recordInfoResponse = try decoder.decode(RecordInfoResponse.self, from: responseData)
  } catch {
    // If JSON parsing fails, check HTTP status code
    if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode >= 400 {
      throw NewImageGenerationError.serverError("HTTP \(httpResponse.statusCode)")
    }
    throw NewImageGenerationError.serverError("Failed to parse response: \(error.localizedDescription)")
  }

  print("✅ [NewImageGeneration] Live implementation - Record info response parsed successfully!")
  print("✅ [NewImageGeneration] Live implementation - Response success: \(recordInfoResponse.success)")
  print("✅ [NewImageGeneration] Live implementation - Response message: \(recordInfoResponse.message)")
  print("✅ [NewImageGeneration] Live implementation - Is success: \(recordInfoResponse.isSuccess)")

  if let data = recordInfoResponse.data {
    print("✅ [NewImageGeneration] Live implementation - Generation record data:")
    print("   - Task ID: \(data.taskId)")
    print("   - Status: \(data.status)")
    print("   - Success Flag: \(data.successFlag)")
    print("   - Progress: \(data.progress)")
    print("   - Is Success: \(data.isSuccess)")
    print("   - Image URLs count: \(data.imageUrls.count)")

    if let errorCode = data.errorCode {
      print("   - Error Code: \(errorCode)")
    }
    if let errorMessage = data.errorMessage {
      print("   - Error Message: \(errorMessage)")
    }
    if let completeTime = data.completeTime {
      print("   - Complete Time: \(completeTime)")
    }
  } else {
    print("ℹ️ [NewImageGeneration] Live implementation - No generation record data (task may still be processing)")
  }

  // 检查业务逻辑是否成功
  if !recordInfoResponse.isSuccess {
    print("❌ [NewImageGeneration] Live implementation - Status check failed")

    // 根据错误信息创建相应的错误
    if let error = recordInfoResponse.error {
      throw NewImageGenerationError.serverError("Generation failed: \(error)")
    } else {
      throw NewImageGenerationError.serverError("Generation failed: \(recordInfoResponse.message)")
    }
  } else {
    print("✅ [NewImageGeneration] Live implementation - Task status check successful!")
  }

  // 转换为NewTaskStatusResponse格式以保持兼容性
  let statusResponse = NewTaskStatusResponse(
    code: recordInfoResponse.code == 200 ? 0 : recordInfoResponse.code,
    message: recordInfoResponse.message,
    data: recordInfoResponse.data
  )

  return statusResponse
}

extension NewImageGenerationClient: TestDependencyKey {
  public static let testValue = Self()
}

extension DependencyValues {
  public var newImageGenerationClient: NewImageGenerationClient {
    get { self[NewImageGenerationClient.self] }
    set { self[NewImageGenerationClient.self] = newValue }
  }
}
