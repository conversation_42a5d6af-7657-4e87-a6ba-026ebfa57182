import SwiftUI
import ComposableArchitecture
import StyleSelectionCore
import CommonUI

// MARK: - Style Selection View

public struct StyleSelectionView: View {
  let store: StoreOf<StyleSelection>
  @State private var selectedCategory: StyleCategory = .all
  
  public init(store: StoreOf<StyleSelection>) {
    self.store = store
  }
  
  public var body: some View {
    NavigationView {
      VStack(spacing: 0) {
        if store.isLoading {
          loadingSection
        } else {
          mainContentSection
        }
        
        if store.canProceed {
          actionButtonSection
        }
      }
      .navigationTitle("选择风格")
      .neumorphicNavigationStyle()
      .neumorphicBackground()
      .onAppear {
        store.send(.onAppear)
      }
      .alert(
        item: Binding(
          get: { store.error.map(ErrorWrapper.init) },
          set: { _ in store.send(.dismissError) }
        )
      ) { errorWrapper in
        Alert(
          title: Text("提示"),
          message: Text(errorWrapper.error.localizedDescription),
          dismissButton: .default(Text("确定"))
        )
      }
      .sheet(isPresented: Binding(
        get: { store.isShowingCustomPrompt },
        set: { _ in store.send(.hideCustomPrompt) }
      )) {
        CustomPromptSheet(store: store)
      }
      .sheet(isPresented: Binding(
        get: { store.isShowingGenerationConfirmation },
        set: { _ in store.send(.hideGenerationConfirmation) }
      )) {
        GenerationConfirmationSheet(store: store)
      }
    }
  }
  
  // MARK: - 🌸 新拟物风格头部区域
  
  private var headerSection: some View {
    VStack(spacing: NeumorphicDesign.largeSpacing) {
      // 新拟物风格图标
      ZStack {
        Circle()
          .fill(
            LinearGradient(
              colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 100, height: 100)
          .shadow(color: .shadowDark.opacity(0.2), radius: 12, x: 4, y: 4)
          .shadow(color: .shadowLight, radius: 12, x: -4, y: -4)

        Image(systemName: "paintbrush.pointed.fill")
          .font(.system(size: 40, weight: .light))
          .foregroundStyle(
            LinearGradient(
              colors: [.softPink, .warmOrange],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
      }
      
      VStack(spacing: NeumorphicDesign.smallSpacing) {
        Text("选择您喜欢的风格")
          .font(.welcomeTitle)
          .fontWeight(.bold)
          .foregroundColor(.textPrimary)
        
        Text("AI将根据您的选择生成专属婚纱照")
          .font(.welcomeSubtitle)
          .foregroundColor(.textSecondary)
          .multilineTextAlignment(.center)
          .lineSpacing(4)
      }
    }
    .padding(.vertical, NeumorphicDesign.largePadding)
  }
  
  // MARK: - 🌸 新拟物风格分类筛选区域
  
  private var categoryFilterSection: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      HStack(spacing: NeumorphicDesign.smallSpacing) {
        ForEach(StyleCategory.allCases, id: \.self) { category in
          CategoryChip(
            category: category,
            isSelected: selectedCategory == category
          ) {
            selectedCategory = category
          }
        }
      }
      .padding(.horizontal, NeumorphicDesign.largePadding)
    }
    .padding(.vertical, NeumorphicDesign.smallSpacing)
  }
  
  // MARK: - 🌸 新拟物风格加载区域
  
  private var loadingSection: some View {
    VStack(spacing: NeumorphicDesign.largeSpacing) {
      EnhancedLoadingView(message: "加载风格中...")
      
      Text("为您准备精美风格选项")
        .font(.customBody)
        .foregroundColor(.textSecondary)
        .multilineTextAlignment(.center)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .padding(NeumorphicDesign.largePadding)
  }
  
  // MARK: - 🌸 主内容区域
  
  private var mainContentSection: some View {
    ScrollView {
      VStack(spacing: NeumorphicDesign.largeSpacing) {
        headerSection
        categoryFilterSection
        stylesGridContent
      }
    }
  }
  
  // MARK: - 🌸 新拟物风格样式网格内容
  
  private var stylesGridContent: some View {
    LazyVGrid(
      columns: Array(repeating: GridItem(.flexible(), spacing: NeumorphicDesign.mediumSpacing), count: 2),
      spacing: NeumorphicDesign.mediumSpacing
    ) {
      ForEach(filteredStyles) { style in
        StyleCard(
          style: style,
          isSelected: store.selectedStyle?.id == style.id
        ) {
          store.send(.styleSelected(style))
        }
      }
    }
    .padding(.horizontal, NeumorphicDesign.largePadding)
    .padding(.bottom, 120) // Space for action button
  }
  
  // MARK: - 🌸 新拟物风格操作按钮区域
  
  private var actionButtonSection: some View {
    VStack(spacing: 0) {
      // 分隔线
      Rectangle()
        .fill(Color.shadowDark.opacity(0.1))
        .frame(height: 1)
      
      ContentCard(style: .flat) {
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          if let selectedStyle = store.selectedStyle {
            HStack {
              VStack(alignment: .leading, spacing: 4) {
                Text("已选择: \(selectedStyle.name)")
                  .font(.customBodyMedium)
                  .foregroundColor(.textPrimary)
                
                if selectedStyle.isPremium {
                  HStack(spacing: 4) {
                    Image(systemName: "crown.fill")
                      .font(.system(size: 12))
                      .foregroundColor(.warmOrange)
                    Text("VIP专享")
                      .font(.customCaption)
                      .foregroundColor(.textSecondary)
                  }
                }
              }
              
              Spacer()
            }
          }
          
          PrimaryButton(
            title: "开始生成",
            style: .primary,
            action: {
              store.send(.showGenerationConfirmation)
            }
          )
        }
      }
      .padding(.horizontal, NeumorphicDesign.largePadding)
      .padding(.vertical, NeumorphicDesign.mediumPadding)
      .background(Color.creamBackground)
    }
  }
  
  // MARK: - Computed Properties
  
  private var filteredStyles: [WeddingStyle] {
    let styles = store.availableStyles
    
    switch selectedCategory {
    case .all:
      return styles
    case .premium:
      return styles.filter { $0.isPremium }
    case .classic:
      return styles.filter { $0.tags.contains("经典") }
    case .romantic:
      return styles.filter { $0.tags.contains("浪漫") }
    case .modern:
      return styles.filter { $0.tags.contains("现代") }
    case .vintage:
      return styles.filter { $0.tags.contains("复古") }
    case .outdoor:
      return styles.filter { $0.tags.contains("户外") || $0.tags.contains("海滩") || $0.tags.contains("森林") }
    }
  }
}

// MARK: - Supporting Views

// MARK: - 🌸 新拟物风格分类标签

struct CategoryChip: View {
  let category: StyleCategory
  let isSelected: Bool
  let action: () -> Void
  
  @State private var isPressed = false
  
  var body: some View {
    Button(action: {
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .light)
      impactFeedback.impactOccurred()
      #endif
      action()
    }) {
      HStack(spacing: 6) {
        Image(systemName: category.systemImage)
          .font(.system(size: 12, weight: .medium))
        
        Text(category.displayName)
          .font(.customTag)
          .fontWeight(.semibold)
      }
      .padding(.horizontal, NeumorphicDesign.smallPadding)
      .padding(.vertical, 8)
      .background(
        ZStack {
          RoundedRectangle(cornerRadius: NeumorphicDesign.largeRadius)
            .fill(
              isSelected 
                ? LinearGradient(
                    colors: [.softPink, .warmOrange],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                : LinearGradient(
                    colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
            )
          
          // 内阴影效果（未选中状态）
          if !isSelected {
            RoundedRectangle(cornerRadius: NeumorphicDesign.largeRadius)
              .stroke(Color.shadowInner, lineWidth: 1)
              .blur(radius: 0.5)
          }
        }
      )
      .foregroundColor(isSelected ? .white : .textPrimary)
      .scaleEffect(isPressed ? 0.95 : 1.0)
      .shadow(
        color: isSelected ? .softPink.opacity(0.3) : .shadowDark.opacity(0.1),
        radius: isSelected ? 6 : 3,
        x: 0,
        y: 2
      )
    }
    .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
      withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
        isPressed = pressing
      }
    }, perform: {})
  }
}

// MARK: - 🌸 新拟物风格样式卡片

struct StyleCard: View {
  let style: WeddingStyle
  let isSelected: Bool
  let action: () -> Void
  
  @State private var isPressed = false
  
  var body: some View {
    Button(action: {
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .light)
      impactFeedback.impactOccurred()
      #endif
      action()
    }) {
      VStack(spacing: 0) {
        // 🌸 新拟物风格预览区域
        ZStack {
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .fill(
              LinearGradient(
                colors: [.softPink.opacity(0.4), .warmOrange.opacity(0.4)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(height: 120)
            .shadow(color: .shadowDark.opacity(0.15), radius: 4, x: 0, y: 2)
          
          if style.isCustom {
            VStack(spacing: 8) {
              ZStack {
                Circle()
                  .fill(Color.white.opacity(0.9))
                  .frame(width: 40, height: 40)
                  .shadow(color: .shadowDark.opacity(0.2), radius: 4, x: 0, y: 2)
                
                Image(systemName: "plus")
                  .font(.system(size: 18, weight: .medium))
                  .foregroundColor(.textPrimary)
              }
              
              Text("自定义")
                .font(.customCaption)
                .fontWeight(.medium)
                .foregroundColor(.white)
            }
          } else {
            ZStack {
              Circle()
                .fill(Color.white.opacity(0.9))
                .frame(width: 40, height: 40)
                .shadow(color: .shadowDark.opacity(0.2), radius: 4, x: 0, y: 2)
              
              Image(systemName: "photo")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.textPrimary)
            }
          }
          
          // VIP标识
          if style.isPremium {
            VStack {
              HStack {
                Spacer()
                
                ColorfulTag(
                  text: "VIP",
                  backgroundColor: .warmOrange,
                  textColor: .white
                )
                .padding(8)
              }
              
              Spacer()
            }
          }
          
          // 选中指示器
          if isSelected {
            VStack {
              Spacer()
              HStack {
                Spacer()
                
                ZStack {
                  Circle()
                    .fill(Color.softPink)
                    .frame(width: 28, height: 28)
                    .shadow(color: .softPink.opacity(0.3), radius: 4, x: 0, y: 2)
                  
                  Image(systemName: "checkmark")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
                }
                .padding(8)
              }
            }
          }
        }
        
        // 🌸 新拟物风格信息区域
        VStack(alignment: .leading, spacing: NeumorphicDesign.smallSpacing) {
          Text(style.name)
            .font(.customBodyMedium)
            .fontWeight(.semibold)
            .foregroundColor(.textPrimary)
            .multilineTextAlignment(.leading)
            .lineLimit(1)
          
          Text(style.description)
            .font(.customCaption)
            .foregroundColor(.textSecondary)
            .multilineTextAlignment(.leading)
            .lineLimit(2)
          
          // 标签
          if !style.tags.isEmpty {
            HStack(spacing: 4) {
              ForEach(style.tags.prefix(2), id: \.self) { tag in
                ColorfulTag(
                  text: tag,
                  backgroundColor: .lightPurple,
                  textColor: .textPrimary
                )
              }
              Spacer()
            }
          }
        }
        .padding(NeumorphicDesign.smallPadding)
        .frame(maxWidth: .infinity, alignment: .leading)
      }
      .background(
        ZStack {
          // 新拟物背景
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .fill(
              LinearGradient(
                colors: [.white.opacity(0.95), .softBeige.opacity(0.8)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
          
          // 选中状态的边框
          if isSelected {
            RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
              .stroke(
                LinearGradient(
                  colors: [.softPink, .warmOrange],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                ),
                lineWidth: 2
              )
          }
        }
      )
      .scaleEffect(isPressed ? 0.98 : 1.0)
      .shadow(
        color: isSelected ? .softPink.opacity(0.3) : .shadowDark.opacity(0.1),
        radius: isSelected ? 8 : 4,
        x: 0,
        y: isSelected ? 4 : 2
      )
    }
    .buttonStyle(PlainButtonStyle())
    .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
      withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
        isPressed = pressing
      }
    }, perform: {})
  }
}

struct CustomPromptSheet: View {
  let store: StoreOf<StyleSelection>
  
  var body: some View {
    NavigationView {
      VStack(spacing: 24) {
        VStack(spacing: 16) {
          Image(systemName: "wand.and.stars")
            .font(.system(size: 40))
            .foregroundStyle(
              LinearGradient(
                colors: [.pink, .purple],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
          
          VStack(spacing: 8) {
            Text("自定义风格")
              .font(.title2)
              .fontWeight(.bold)
            
            Text("描述您想要的婚纱照风格，AI将为您创造独特的作品")
              .font(.subheadline)
              .foregroundColor(.secondary)
              .multilineTextAlignment(.center)
          }
        }
        
        VStack(alignment: .leading, spacing: 8) {
          Text("风格描述")
            .font(.headline)
          
          TextEditor(text: Binding(
            get: { store.customPrompt },
            set: { store.send(.customPromptChanged($0)) }
          ))
            .frame(minHeight: 120)
            .padding(12)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
          
          Text("例如：海边日落，轻纱飘逸，温暖光线")
            .font(.caption)
            .foregroundColor(.secondary)
        }
        
        Spacer()
        
        Button("确认选择") {
          store.send(.hideCustomPrompt)
        }
        .font(.headline)
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
          LinearGradient(
            colors: [.pink, .purple],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(12)
        .disabled(store.customPrompt.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
      }
      .padding(20)
      .navigationTitle("自定义风格")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .toolbar {
        ToolbarItem(placement: .automatic) {
          Button("取消") {
            store.send(.hideCustomPrompt)
          }
        }
      }
    }
  }
}

// MARK: - Generation Confirmation Sheet

struct GenerationConfirmationSheet: View {
  let store: StoreOf<StyleSelection>

  var body: some View {
    NavigationView {
      VStack(spacing: 24) {
        // Header Section
        VStack(spacing: 12) {
          Image(systemName: "sparkles")
            .font(.system(size: 48))
            .foregroundColor(.pink)

          Text("确认生成")
            .font(.title2)
            .fontWeight(.bold)

          Text("即将为您生成专属的婚纱照片")
            .font(.subheadline)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }

        // Selected Style Section
        if let selectedStyle = store.selectedStyle {
          VStack(alignment: .leading, spacing: 16) {
            Text("选择的风格")
              .font(.headline)

            HStack(spacing: 16) {
              // Style Preview
              ZStack {
                RoundedRectangle(cornerRadius: 12)
                  .fill(
                    LinearGradient(
                      colors: [.pink.opacity(0.3), .purple.opacity(0.3)],
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    )
                  )
                  .frame(width: 80, height: 80)

                if selectedStyle.isCustom {
                  Image(systemName: "plus.circle")
                    .font(.system(size: 24))
                    .foregroundColor(.primary)
                } else {
                  Image(systemName: "photo")
                    .font(.system(size: 24))
                    .foregroundColor(.primary)
                }
              }

              // Style Details
              VStack(alignment: .leading, spacing: 4) {
                HStack {
                  Text(selectedStyle.name)
                    .font(.headline)

                  if selectedStyle.isPremium {
                    Image(systemName: "crown.fill")
                      .foregroundColor(.yellow)
                      .font(.caption)
                  }
                }

                Text(selectedStyle.description)
                  .font(.subheadline)
                  .foregroundColor(.secondary)
                  .lineLimit(2)

                if selectedStyle.isCustom && !store.customPrompt.isEmpty {
                  Text("自定义描述: \(store.customPrompt)")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .lineLimit(3)
                }
              }

              Spacer()
            }
            .padding(16)
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
          }
        }

        // Generation Info Section
        VStack(alignment: .leading, spacing: 12) {
          Text("生成信息")
            .font(.headline)

          VStack(spacing: 8) {
            HStack {
              Image(systemName: "clock")
                .foregroundColor(.blue)
              Text("预计时间: 30-60秒")
                .font(.subheadline)
              Spacer()
            }

            HStack {
              Image(systemName: "photo.on.rectangle")
                .foregroundColor(.green)
              Text("将生成高质量婚纱照片")
                .font(.subheadline)
              Spacer()
            }

            HStack {
              Image(systemName: "checkmark.circle")
                .foregroundColor(.orange)
              Text("可保存到相册")
                .font(.subheadline)
              Spacer()
            }
          }
          .padding(16)
          .background(Color.gray.opacity(0.05))
          .cornerRadius(12)
        }

        Spacer()

        // Action Buttons
        VStack(spacing: 12) {
          Button("确认生成") {
            store.send(.confirmGeneration)
          }
          .font(.headline)
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .padding(.vertical, 16)
          .background(
            LinearGradient(
              colors: [.pink, .purple],
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(12)

          Button("取消") {
            store.send(.hideGenerationConfirmation)
          }
          .font(.subheadline)
          .foregroundColor(.secondary)
        }
      }
      .padding(20)
      .navigationTitle("确认生成")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .toolbar {
        ToolbarItem(placement: .automatic) {
          Button("取消") {
            store.send(.hideGenerationConfirmation)
          }
        }
      }
    }
  }
}

// MARK: - Error Wrapper

struct ErrorWrapper: Identifiable {
  let id = UUID()
  let error: StyleSelectionError
}

// MARK: - Previews

#Preview("Style Selection") {
  StyleSelectionView(
    store: Store(initialState: StyleSelection.State()) {
      StyleSelection()
    }
  )
}

#Preview("With Selection") {
  StyleSelectionView(
    store: Store(
      initialState: StyleSelection.State(
        selectedStyle: WeddingStyle.mockStyles.first
      )
    ) {
      StyleSelection()
    }
  )
}
