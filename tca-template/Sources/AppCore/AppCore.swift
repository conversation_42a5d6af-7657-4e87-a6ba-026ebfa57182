import Foundation
import ComposableArchitecture
import LoginCore
import LaunchCore
import MainTabCore
import SubscriptionCore
import UserStateCore
import UserStorageClient
import LoggingClient

// MARK: - Bridal AI App Feature

@Reducer
public enum AppFeature {
  case launch(Launch)
  case login(Login)
  case mainTab(MainTab)  // 新的TabBar主页面

  public static var body: some ReducerOf<Self> {
    Reduce<State, Action> { state, action in
      @Dependency(\.loggingClient) var logger
      @Dependency(\.userStorageClient) var userStorageClient
      
      switch action {
      // Launch Screen Logic
      case .launch(.checkAuthenticationStatus):
        logger.info(.app, "🚀 AppCore: Checking authentication status")
        return .none
        
      case .launch(.userIsAuthenticated(let user, _)):
        logger.info(.app, "✅ AppCore: User authenticated (\(user.displayName)), navigating to main tab")
        print("🔄 AppCore: Auto-login successful for user: \(user.email)")
        print("📱 Starting main tab interface")
        state = .mainTab(MainTab.State(user: user))
        return .none
        
      case .launch(.userNotAuthenticated):
        logger.info(.app, "❌ AppCore: User not authenticated, showing login options")
        print("🔄 AppCore: Transitioning from launch to login state")
        state = .login(Login.State())
        return .none

      // Login Flow - Navigate to main tab after successful authentication
      case .login(.loginResponse(.success(let response))):
        logger.info(.app, "Login successful, navigating to main tab")

        // 从登录响应中获取用户数据并传递给MainTab
        if let authenticatedUser = response.user {
          print("🔄 AppCore: 登录成功，准备设置MainTab状态")
          print("   认证用户: \(authenticatedUser.displayName) (\(authenticatedUser.email))")
          print("   Token类型: \(response.token.contains("mock") ? "Mock Token" : "Real Token")")
          
          // 先创建默认的MainTab状态，然后异步加载用户数据
          state = .mainTab(MainTab.State())

          return .run { send in
            // 等待LoginCore完成数据保存
            print("⏳ AppCore: 等待LoginCore完成Keychain保存...")
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒，给更多时间

            do {
              print("🔍 AppCore: 尝试从Keychain加载用户数据...")
              // 从Keychain加载完整的用户数据（包括最新的订阅状态）
              if let (user, token) = try await userStorageClient.loadUser() {
                print("✅ AppCore: 从Keychain加载用户数据成功")
                print("   用户: \(user.displayName) (\(user.email))")
                print("   订阅状态: \(user.subscriptionStatus.displayName)")
                print("   Token: \(token.prefix(20))...")

                // 发送action到MainTab来设置用户数据
                await send(.mainTab(.setUserData(user)))
              } else {
                print("⚠️ AppCore: 无法从Keychain加载用户数据，使用登录响应数据创建用户")
                
                // 使用登录响应中的数据创建用户对象
                let fallbackUser = User(
                  id: authenticatedUser.id,
                  email: authenticatedUser.email,
                  displayName: authenticatedUser.displayName,
                  avatarURL: authenticatedUser.avatarURL,
                  createdAt: Date(),
                  subscriptionStatus: .free // 默认免费状态
                )
                
                print("🔄 AppCore: 使用fallback用户数据: \(fallbackUser.displayName)")
                await send(.mainTab(.setUserData(fallbackUser)))
                
                // 尝试手动保存到Keychain
                do {
                  print("🔄 AppCore: 尝试手动保存用户数据到Keychain...")
                  try await userStorageClient.saveUser(fallbackUser, response.token)
                  print("✅ AppCore: 手动保存成功")
                } catch {
                  print("❌ AppCore: 手动保存失败: \(error)")
                }
              }
            } catch {
              print("❌ AppCore: 加载用户数据过程中出错: \(error)")
              
              // 创建基本用户数据作为最后的备用方案
              let emergencyUser = User(
                id: authenticatedUser.id,
                email: authenticatedUser.email,
                displayName: authenticatedUser.displayName,
                avatarURL: authenticatedUser.avatarURL,
                createdAt: Date(),
                subscriptionStatus: .free
              )
              
              print("🆘 AppCore: 使用紧急备用用户数据: \(emergencyUser.displayName)")
              await send(.mainTab(.setUserData(emergencyUser)))
            }
          }
        } else {
          print("⚠️ AppCore: 登录成功但没有用户数据，创建默认MainTab")
          state = .mainTab(MainTab.State())
          return .none
        }

      // MainTab Actions - Handle logout from profile
      case .mainTab(.profile(.logoutButtonTapped)):
        logger.info(.app, "User logout initiated from profile")
        // Return to login screen after logout
        state = .login(Login.State())
        return .none
        
      case .mainTab(.profile(.deleteAccountCompleted)):
        logger.info(.app, "Account deletion completed, returning to login")
        // Return to login screen after account deletion
        state = .login(Login.State())
        return .none

      default:
        return .none
      }
    }
    .ifCaseLet(\.launch, action: \.launch) {
      Launch()
    }
    .ifCaseLet(\.login, action: \.login) {
      Login()
    }
    .ifCaseLet(\.mainTab, action: \.mainTab) {
      MainTab()
    }
  }
}

extension AppFeature.State: Equatable {}
