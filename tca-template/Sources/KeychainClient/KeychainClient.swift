import Dependencies
import DependenciesMacros
import Foundation
import Security

// MARK: - Keychain Client

@DependencyClient
public struct KeychainClient: Sendable {
  public var save: @Sendable (_ key: String, _ data: Data) async throws -> Void
  public var load: @Sendable (_ key: String) async throws -> Data?
  public var delete: @Sendable (_ key: String) async throws -> Void
  public var deleteAll: @Sendable () async throws -> Void
}

// MARK: - Keychain Error

public enum KeychainError: Error, Equatable, LocalizedError {
  case itemNotFound
  case duplicateItem
  case invalidData
  case unexpectedStatus(OSStatus)
  case encodingError
  case decodingError
  
  public var errorDescription: String? {
    switch self {
    case .itemNotFound:
      return "Keychain item not found"
    case .duplicateItem:
      return "Keychain item already exists"
    case .invalidData:
      return "Invalid data provided"
    case .unexpectedStatus(let status):
      return "Unexpected keychain status: \(status)"
    case .encodingError:
      return "Failed to encode data"
    case .decodingError:
      return "Failed to decode data"
    }
  }
}

// MARK: - Live Implementation

extension KeychainClient: DependencyKey {
  public static let liveValue = Self(
    save: { key, data in
      let deleteQuery: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: "com.bridal.app"
      ]
      
      // Delete existing item first (ignore result)
      let deleteStatus = SecItemDelete(deleteQuery as CFDictionary)
      print("🔧 KeychainClient: 删除已存在的key '\(key)' 状态: \(deleteStatus)")
      
      // Add new item
      let addQuery: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: "com.bridal.app",
        kSecValueData as String: data,
        kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
      ]
      
      let addStatus = SecItemAdd(addQuery as CFDictionary, nil)
      print("🔧 KeychainClient: 添加新key '\(key)' 状态: \(addStatus)")
      
      if addStatus == errSecSuccess {
        print("✅ KeychainClient: 成功保存key '\(key)'")
      } else if addStatus == errSecDuplicateItem {
        // 如果仍然重复，尝试更新
        print("🔄 KeychainClient: 尝试更新已存在的key '\(key)'")
        let updateQuery: [String: Any] = [
          kSecClass as String: kSecClassGenericPassword,
          kSecAttrAccount as String: key,
          kSecAttrService as String: "com.bridal.app"
        ]
        let updateAttributes: [String: Any] = [
          kSecValueData as String: data
        ]
        let updateStatus = SecItemUpdate(updateQuery as CFDictionary, updateAttributes as CFDictionary)
        print("🔄 KeychainClient: 更新key '\(key)' 状态: \(updateStatus)")
        
        if updateStatus == errSecSuccess {
          print("✅ KeychainClient: 成功更新key '\(key)'")
        } else {
          print("❌ KeychainClient: 更新失败 - key: \(key), status: \(updateStatus)")
          throw KeychainError.unexpectedStatus(updateStatus)
        }
      } else {
        print("❌ KeychainClient: 保存失败 - key: \(key), status: \(addStatus)")
        throw KeychainError.unexpectedStatus(addStatus)
      }
    },
    
    load: { key in
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: "com.bridal.app",
        kSecReturnData as String: true,
        kSecMatchLimit as String: kSecMatchLimitOne
      ]
      
      var result: AnyObject?
      let status = SecItemCopyMatching(query as CFDictionary, &result)
      
      guard status == errSecSuccess else {
        if status == errSecItemNotFound {
          return nil
        }
        throw KeychainError.unexpectedStatus(status)
      }
      
      guard let data = result as? Data else {
        throw KeychainError.invalidData
      }
      
      return data
    },
    
    delete: { key in
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: "com.bridal.app"
      ]
      
      let status = SecItemDelete(query as CFDictionary)
      
      guard status == errSecSuccess || status == errSecItemNotFound else {
        throw KeychainError.unexpectedStatus(status)
      }
    },
    
    deleteAll: {
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrService as String: "com.bridal.app"
      ]
      
      let status = SecItemDelete(query as CFDictionary)
      
      guard status == errSecSuccess || status == errSecItemNotFound else {
        throw KeychainError.unexpectedStatus(status)
      }
    }
  )
}

// MARK: - Test Implementation

extension KeychainClient: TestDependencyKey {
  public static let testValue = Self(
    save: { _, _ in },
    load: { _ in nil },
    delete: { _ in },
    deleteAll: { }
  )
}

// MARK: - Dependency Registration

extension DependencyValues {
  public var keychainClient: KeychainClient {
    get { self[KeychainClient.self] }
    set { self[KeychainClient.self] = newValue }
  }
}

// MARK: - Convenience Extensions

public extension KeychainClient {
  func save<T: Codable>(_ value: T, forKey key: String) async throws {
    let encoder = JSONEncoder()
    let data = try encoder.encode(value)
    try await save(key, data)
  }
  
  func load<T: Codable>(_ type: T.Type, forKey key: String) async throws -> T? {
    guard let data = try await load(key) else { return nil }
    let decoder = JSONDecoder()
    return try decoder.decode(type, from: data)
  }
  
  func saveString(_ string: String, forKey key: String) async throws {
    guard let data = string.data(using: .utf8) else {
      throw KeychainError.encodingError
    }
    try await save(key, data)
  }
  
  func loadString(forKey key: String) async throws -> String? {
    guard let data = try await load(key) else { return nil }
    guard let string = String(data: data, encoding: .utf8) else {
      throw KeychainError.decodingError
    }
    return string
  }
}

// MARK: - Keychain Keys

public enum KeychainKeys {
  public static let accessToken = "access_token"
  public static let refreshToken = "refresh_token"
  public static let userID = "user_id"
  public static let userEmail = "user_email"
  public static let userDisplayName = "user_display_name"
  public static let authProvider = "auth_provider"
  public static let subscriptionStatus = "subscription_status"
  public static let lastLoginDate = "last_login_date"
}
