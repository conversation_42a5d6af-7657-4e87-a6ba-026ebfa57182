import Foundation
import Security

// MARK: - Keychain调试工具

public struct KeychainDebugger {
  public static func debugKeychainContents() {
    print("🔍 [KeychainDebugger] 开始检查Keychain内容...")
    
    let service = "com.bridal.app"
    let keys = ["access_token", "user_id", "subscription_status"]
    
    for key in keys {
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: service,
        kSecReturnData as String: true,
        kSecMatchLimit as String: kSecMatchLimitOne
      ]
      
      var result: AnyObject?
      let status = SecItemCopyMatching(query as CFDictionary, &result)
      
      switch status {
      case errSecSuccess:
        if let data = result as? Data {
          print("✅ [KeychainDebugger] Key '\(key)' 存在，数据大小: \(data.count) bytes")
          
          // 尝试解析数据
          if key == "access_token" {
            if let token = String(data: data, encoding: .utf8) {
              print("   Token内容: \(token.prefix(20))...")
            }
          } else if key == "user_id" {
            // 尝试解析用户数据
            do {
              let decoder = JSONDecoder()
              let userData = try decoder.decode(StoredUserData.self, from: data)
              print("   用户数据: \(userData.displayName) (\(userData.email))")
              print("   创建时间: \(userData.createdAt)")
              print("   最后登录: \(userData.lastLoginDate)")
            } catch {
              print("   ⚠️ 无法解析用户数据: \(error)")
            }
          }
        }
      case errSecItemNotFound:
        print("❌ [KeychainDebugger] Key '\(key)' 不存在")
      default:
        print("❌ [KeychainDebugger] Key '\(key)' 查询失败，状态: \(status)")
      }
    }
    
    print("🔍 [KeychainDebugger] Keychain检查完成")
  }
  
  public static func clearAllKeychainData() {
    print("🗑️ [KeychainDebugger] 开始清除所有Keychain数据...")
    
    let service = "com.bridal.app"
    let keys = ["access_token", "user_id", "subscription_status"]
    
    for key in keys {
      let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: key,
        kSecAttrService as String: service
      ]
      
      let status = SecItemDelete(query as CFDictionary)
      switch status {
      case errSecSuccess:
        print("✅ [KeychainDebugger] 成功删除key '\(key)'")
      case errSecItemNotFound:
        print("ℹ️ [KeychainDebugger] Key '\(key)' 不存在，无需删除")
      default:
        print("❌ [KeychainDebugger] 删除key '\(key)' 失败，状态: \(status)")
      }
    }
    
    print("🗑️ [KeychainDebugger] Keychain清除完成")
  }
}

// MARK: - StoredUserData定义（用于调试）

struct StoredUserData: Codable {
  let id: String
  let email: String
  let displayName: String
  let avatarURL: String?
  let createdAt: Date
  let subscriptionStatusData: Data
  let authProvider: String
  let lastLoginDate: Date
}