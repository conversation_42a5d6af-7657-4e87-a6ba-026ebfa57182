import ComposableArchitecture
import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - 🌸 新拟物风格设计系统常量

public enum NeumorphicDesign {
  // 圆角系统
  public static let smallRadius: CGFloat = 12
  public static let mediumRadius: CGFloat = 16
  public static let largeRadius: CGFloat = 20
  public static let cardRadius: CGFloat = 24
  public static let iconRadius: CGFloat = 28
  
  // 间距系统
  public static let smallSpacing: CGFloat = 8
  public static let mediumSpacing: CGFloat = 16
  public static let largeSpacing: CGFloat = 24
  public static let extraLargeSpacing: CGFloat = 32
  
  // 内边距系统
  public static let smallPadding: CGFloat = 12
  public static let mediumPadding: CGFloat = 16
  public static let largePadding: CGFloat = 20
  public static let extraLargePadding: CGFloat = 24
  
  // 阴影系统
  public static let lightShadowRadius: CGFloat = 4
  public static let mediumShadowRadius: CGFloat = 8
  public static let largeShadowRadius: CGFloat = 12
  public static let extraLargeShadowRadius: CGFloat = 20
  
  // 动画时长
  public static let quickAnimation: Double = 0.2
  public static let standardAnimation: Double = 0.3
  public static let slowAnimation: Double = 0.5
}

// MARK: - Common UI Components

/// 🌸 新拟物风格按钮组件 - 柔和渐变 + 内阴影效果
public struct PrimaryButton: View {
  let title: String
  let action: () -> Void
  let isLoading: Bool
  let isDisabled: Bool
  let style: ButtonStyle

  @State private var isPressed = false

  public enum ButtonStyle {
    case primary      // 主要按钮 - 粉橙渐变
    case secondary    // 次要按钮 - 新拟物卡片风格
    case outline      // 轮廓按钮 - 透明背景
    case soft         // 柔和按钮 - 淡色背景

    var backgroundGradient: LinearGradient {
      switch self {
      case .primary: 
        return LinearGradient(
          colors: [.softPink, .warmOrange],
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
      case .secondary, .soft:
        return LinearGradient(
          colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
      case .outline:
        return LinearGradient(
          colors: [.clear, .clear],
          startPoint: .top,
          endPoint: .bottom
        )
      }
    }

    var foregroundColor: Color {
      switch self {
      case .primary: return .white
      case .secondary, .soft: return .textPrimary
      case .outline: return .softPink
      }
    }

    var borderColor: Color {
      switch self {
      case .primary, .secondary, .soft: return .clear
      case .outline: return .softPink.opacity(0.6)
      }
    }
    
    var shadowColor: Color {
      switch self {
      case .primary: return .softPink.opacity(0.3)
      case .secondary, .soft: return .shadowDark
      case .outline: return .clear
      }
    }
  }

  public init(
    title: String,
    style: ButtonStyle = .primary,
    isLoading: Bool = false,
    isDisabled: Bool = false,
    action: @escaping () -> Void
  ) {
    self.title = title
    self.style = style
    self.isLoading = isLoading
    self.isDisabled = isDisabled
    self.action = action
  }

  public var body: some View {
    Button(action: {
      // 柔和触觉反馈
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .light)
      impactFeedback.impactOccurred()
      #endif
      action()
    }) {
      HStack(spacing: 12) {
        if isLoading {
          ProgressView()
            .progressViewStyle(CircularProgressViewStyle(tint: style.foregroundColor))
            .scaleEffect(0.9)
        }

        Text(title)
          .font(.customButton)
          .fontWeight(.semibold)
      }
      .foregroundColor(isDisabled ? .textTertiary : style.foregroundColor)
      .frame(maxWidth: .infinity)
      .padding(.vertical, 16)
      .padding(.horizontal, 24)
      .background(
        ZStack {
          // 新拟物背景效果
          RoundedRectangle(cornerRadius: 20)
            .fill(style.backgroundGradient)
          
          // 内阴影效果（仅非primary按钮）
          if style != .primary {
            RoundedRectangle(cornerRadius: 20)
              .stroke(Color.white.opacity(0.8), lineWidth: 1)
              .blur(radius: 0.5)
              .offset(x: -1, y: -1)
            
            RoundedRectangle(cornerRadius: 20)
              .stroke(Color.shadowInner, lineWidth: 1)
              .blur(radius: 0.5)
              .offset(x: 1, y: 1)
          }
        }
      )
      .overlay(
        RoundedRectangle(cornerRadius: 20)
          .stroke(style.borderColor, lineWidth: style == .outline ? 1.5 : 0)
      )
      .scaleEffect(isPressed ? 0.98 : 1.0)
      .opacity(isDisabled ? 0.5 : 1.0)
      .shadow(
        color: style.shadowColor,
        radius: isPressed ? 8 : 12,
        x: 0,
        y: isPressed ? 2 : 4
      )
    }
    .disabled(isDisabled || isLoading)
    .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
      withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
        isPressed = pressing
      }
    }, perform: {})
  }
}

/// A reusable text field component with consistent styling
public struct StyledTextField: View {
  let title: String
  @Binding var text: String
  let placeholder: String
  let isSecure: Bool
  
  public init(
    title: String,
    text: Binding<String>,
    placeholder: String = "",
    isSecure: Bool = false
  ) {
    self.title = title
    self._text = text
    self.placeholder = placeholder
    self.isSecure = isSecure
  }
  
  public var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      Text(title)
        .font(.headline)
        .foregroundColor(.primary)
      
      Group {
        if isSecure {
          SecureField(placeholder, text: $text)
        } else {
          TextField(placeholder, text: $text)
        }
      }
      .textFieldStyle(RoundedBorderTextFieldStyle())
      #if os(iOS)
      .textInputAutocapitalization(.never)
      .autocorrectionDisabled(true)
      #endif
    }
  }
}

/// 🌸 新拟物风格卡片组件 - 柔和内阴影 + 圆角设计
public struct ContentCard<Content: View>: View {
  let content: Content
  let style: CardStyle
  
  public enum CardStyle {
    case elevated    // 浮起效果
    case inset      // 内嵌效果
    case flat       // 平面效果
    case soft       // 柔和效果
  }
  
  public init(style: CardStyle = .soft, @ViewBuilder content: () -> Content) {
    self.content = content()
    self.style = style
  }
  
  public var body: some View {
    content
      .padding(20)
      .background(
        ZStack {
          // 主背景
          RoundedRectangle(cornerRadius: 24)
            .fill(
              LinearGradient(
                colors: [.white.opacity(0.95), .softBeige.opacity(0.8)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
          
          // 新拟物效果
          switch style {
          case .elevated:
            // 外阴影 - 浮起效果
            RoundedRectangle(cornerRadius: 24)
              .fill(Color.clear)
              .shadow(color: .shadowDark, radius: 8, x: 4, y: 4)
              .shadow(color: .shadowLight, radius: 8, x: -4, y: -4)
              
          case .inset:
            // 内阴影 - 内嵌效果
            RoundedRectangle(cornerRadius: 24)
              .stroke(Color.shadowDark, lineWidth: 1)
              .blur(radius: 1)
              .offset(x: 2, y: 2)
              .mask(RoundedRectangle(cornerRadius: 24).fill(LinearGradient(colors: [.clear, .black], startPoint: .topLeading, endPoint: .bottomTrailing)))
            
            RoundedRectangle(cornerRadius: 24)
              .stroke(Color.shadowLight, lineWidth: 1)
              .blur(radius: 1)
              .offset(x: -2, y: -2)
              .mask(RoundedRectangle(cornerRadius: 24).fill(LinearGradient(colors: [.black, .clear], startPoint: .topLeading, endPoint: .bottomTrailing)))
              
          case .flat:
            // 平面效果 - 仅边框
            RoundedRectangle(cornerRadius: 24)
              .stroke(Color.shadowDark.opacity(0.1), lineWidth: 1)
              
          case .soft:
            // 柔和效果 - 轻微阴影
            RoundedRectangle(cornerRadius: 24)
              .fill(Color.clear)
              .shadow(color: .shadowDark.opacity(0.8), radius: 12, x: 0, y: 4)
          }
        }
      )
  }
}

/// A reusable loading view
public struct LoadingView: View {
  let message: String
  
  public init(message: String = "Loading...") {
    self.message = message
  }
  
  public var body: some View {
    VStack(spacing: 16) {
      ProgressView()
        .progressViewStyle(CircularProgressViewStyle())
        .scaleEffect(1.5)

      Text(message)
        .font(.body)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(systemBackgroundColor)
  }
}

/// A reusable error view
public struct ErrorView: View {
  let error: any Error
  let retryAction: (() -> Void)?
  
  public init(error: any Error, retryAction: (() -> Void)? = nil) {
    self.error = error
    self.retryAction = retryAction
  }
  
  public var body: some View {
    VStack(spacing: 16) {
      Image(systemName: "exclamationmark.triangle")
        .font(.system(size: 48))
        .foregroundColor(.red)
      
      Text("Something went wrong")
        .font(.headline)
      
      Text(error.localizedDescription)
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      if let retryAction = retryAction {
        Button("Try Again", action: retryAction)
          .buttonStyle(.borderedProminent)
      }
    }
    .padding()
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(systemBackgroundColor)
  }
}

/// A reusable empty state view
public struct EmptyStateView: View {
  let title: String
  let message: String
  let systemImage: String
  let actionTitle: String?
  let action: (() -> Void)?
  
  public init(
    title: String,
    message: String,
    systemImage: String = "tray",
    actionTitle: String? = nil,
    action: (() -> Void)? = nil
  ) {
    self.title = title
    self.message = message
    self.systemImage = systemImage
    self.actionTitle = actionTitle
    self.action = action
  }
  
  public var body: some View {
    VStack(spacing: 16) {
      Image(systemName: systemImage)
        .font(.system(size: 48))
        .foregroundColor(.secondary)
      
      Text(title)
        .font(.headline)
      
      Text(message)
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      if let actionTitle = actionTitle, let action = action {
        Button(actionTitle, action: action)
          .buttonStyle(.borderedProminent)
      }
    }
    .padding()
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(systemBackgroundColor)
  }
}





// MARK: - View Extensions

public extension View {
  /// 🌸 新拟物风格导航样式
  func neumorphicNavigationStyle() -> some View {
    #if os(iOS)
    return self
      .navigationBarTitleDisplayMode(.inline)
      .toolbarBackground(.hidden, for: .navigationBar)
    #else
    return self
    #endif
  }

  /// 🌸 新拟物风格表单样式
  func neumorphicFormStyle() -> some View {
    self
      .background(Color.backgroundGradient.ignoresSafeArea())
  }

  /// 🌸 新拟物风格现代背景
  func neumorphicBackground() -> some View {
    self
      .background(
        LinearGradient(
          colors: [
            .creamBackground,
            .softBeige.opacity(0.8),
            .lightGray.opacity(0.6)
          ],
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
      )
  }
  
  /// 🌸 新拟物风格卡片效果
  func neumorphicCard(
    cornerRadius: CGFloat = NeumorphicDesign.cardRadius,
    shadowRadius: CGFloat = NeumorphicDesign.mediumShadowRadius
  ) -> some View {
    self
      .background(
        RoundedRectangle(cornerRadius: cornerRadius)
          .fill(Color.cardGradient)
          .shadow(color: .shadowDark.opacity(0.1), radius: shadowRadius, x: 0, y: 4)
      )
  }
  
  /// 🌸 新拟物风格按钮效果
  func neumorphicButton(isPressed: Bool = false) -> some View {
    self
      .scaleEffect(isPressed ? 0.98 : 1.0)
      .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
  }
}

// MARK: - Color Extensions

public extension Color {
  // 🌸 新拟物风格色彩系统 - 奶油米白 + 柔和渐变
  
  // 主色调 - 奶油米白背景系统
  static let creamBackground = Color(red: 0.98, green: 0.97, blue: 0.95) // 奶油白主背景
  static let softBeige = Color(red: 0.96, green: 0.94, blue: 0.91) // 柔和米色
  static let lightGray = Color(red: 0.95, green: 0.95, blue: 0.96) // 高级浅灰
  
  // 强调色 - 粉橙渐变系统
  static let softPink = Color(red: 1.0, green: 0.75, blue: 0.8) // 柔和粉色
  static let warmOrange = Color(red: 1.0, green: 0.8, blue: 0.6) // 温暖橘色
  static let lightPurple = Color(red: 0.9, green: 0.8, blue: 0.95) // 淡紫色
  static let peachPink = Color(red: 1.0, green: 0.85, blue: 0.85) // 桃粉色
  
  // 功能色彩 - 柔和版本
  static let successSoft = Color(red: 0.7, green: 0.9, blue: 0.7) // 柔和成功绿
  static let warningSoft = Color(red: 1.0, green: 0.9, blue: 0.7) // 柔和警告黄
  static let errorSoft = Color(red: 1.0, green: 0.8, blue: 0.8) // 柔和错误红
  
  // 文字颜色系统
  static let textPrimary = Color(red: 0.2, green: 0.2, blue: 0.25) // 主要文字 - 深灰
  static let textSecondary = Color(red: 0.5, green: 0.5, blue: 0.55) // 次要文字 - 中灰
  static let textTertiary = Color(red: 0.7, green: 0.7, blue: 0.75) // 辅助文字 - 浅灰
  
  // 新拟物阴影色彩
  static let shadowLight = Color.white.opacity(0.8) // 高光阴影
  static let shadowDark = Color.black.opacity(0.1) // 深色阴影
  static let shadowInner = Color.black.opacity(0.05) // 内阴影
  
  // 渐变系统 - 粉橙主题
  static let brandGradient = LinearGradient(
    colors: [.softPink, .warmOrange],
    startPoint: .topLeading,
    endPoint: .bottomTrailing
  )
  
  static let softGradient = LinearGradient(
    colors: [.softPink.opacity(0.3), .warmOrange.opacity(0.3)],
    startPoint: .topLeading,
    endPoint: .bottomTrailing
  )
  
  static let backgroundGradient = LinearGradient(
    colors: [.creamBackground, .softBeige],
    startPoint: .top,
    endPoint: .bottom
  )
  
  // 卡片渐变 - 新拟物效果
  static let cardGradient = LinearGradient(
    colors: [.white.opacity(0.9), .softBeige.opacity(0.5)],
    startPoint: .topLeading,
    endPoint: .bottomTrailing
  )
  
  // 深色卡片背景 - 兼容旧版本
  static let darkCardBackground = Color(red: 0.12, green: 0.15, blue: 0.25)
  
  // 向后兼容的颜色别名
  static let primaryAccent = textPrimary // 兼容旧版本
  static let secondaryAccent = softBeige // 兼容旧版本
  static let appBackground = creamBackground // 兼容旧版本
  
  // 强调色别名 - 映射到新拟物风格色彩
  static let accentPink = softPink // 兼容旧版本
  static let accentPurple = lightPurple // 兼容旧版本
  static let accentBlue = Color.blue.opacity(0.7) // 兼容旧版本
  static let accentOrange = warmOrange // 兼容旧版本

  // Semantic Colors
  #if canImport(UIKit)
  static let cardBackground = Color(UIColor.systemBackground)
  static let surfaceBackground = Color(UIColor.secondarySystemBackground)
  static let borderColor = Color(UIColor.separator)
  #else
  static let cardBackground = Color.white
  static let surfaceBackground = Color.gray.opacity(0.1)
  static let borderColor = Color.gray.opacity(0.3)
  #endif
}

// MARK: - Enhanced Card Component

/// A reusable card component with enhanced styling and animations
public struct EnhancedCardView<Content: View>: View {
  let content: Content
  let style: CardStyle
  let isInteractive: Bool

  @State private var isPressed = false

  public enum CardStyle {
    case elevated
    case flat
    case outlined
    case gradient

    var backgroundColor: Color {
      switch self {
      case .elevated, .flat: return .cardBackground
      case .outlined: return .cardBackground
      case .gradient: return .clear
      }
    }

    var shadowRadius: CGFloat {
      switch self {
      case .elevated: return 8
      case .flat: return 0
      case .outlined: return 2
      case .gradient: return 12
      }
    }

    var shadowOpacity: Double {
      switch self {
      case .elevated: return 0.15
      case .flat: return 0
      case .outlined: return 0.05
      case .gradient: return 0.3
      }
    }
  }

  public init(
    style: CardStyle = .elevated,
    isInteractive: Bool = false,
    @ViewBuilder content: () -> Content
  ) {
    self.style = style
    self.isInteractive = isInteractive
    self.content = content()
  }

  public var body: some View {
    content
      .padding()
      .background(
        Group {
          if style == .gradient {
            Color.softGradient
          } else {
            style.backgroundColor
          }
        }
      )
      .overlay(
        RoundedRectangle(cornerRadius: NeumorphicDesign.cardRadius)
          .stroke(style == .outlined ? Color.borderColor : Color.clear, lineWidth: 1)
      )
      .cornerRadius(NeumorphicDesign.cardRadius)
      .scaleEffect(isPressed && isInteractive ? 0.98 : 1.0)
      .shadow(
        color: .black.opacity(style.shadowOpacity),
        radius: isPressed && isInteractive ? style.shadowRadius * 0.7 : style.shadowRadius,
        x: 0,
        y: isPressed && isInteractive ? 2 : 4
      )
      .animation(.easeInOut(duration: NeumorphicDesign.standardAnimation), value: isPressed)
      .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
        if isInteractive {
          withAnimation(.easeInOut(duration: NeumorphicDesign.quickAnimation)) {
            isPressed = pressing
          }
        }
      }, perform: {})
  }
}

// MARK: - Enhanced Loading Indicator

/// A beautiful loading indicator with gradient animation
public struct EnhancedLoadingView: View {
  let message: String
  let progress: Double?

  @State private var rotationAngle: Double = 0
  @State private var scale: CGFloat = 1.0

  public init(message: String = "加载中...", progress: Double? = nil) {
    self.message = message
    self.progress = progress
  }

  public var body: some View {
    VStack(spacing: 20) {
      ZStack {
        // Background circle
        Circle()
          .stroke(Color.gray.opacity(0.2), lineWidth: 4)
          .frame(width: 60, height: 60)

        // Progress circle
        Circle()
          .trim(from: 0, to: progress ?? 1.0)
          .stroke(
            AngularGradient(
              colors: [.pink, .purple, .pink],
              center: .center,
              startAngle: .degrees(0),
              endAngle: .degrees(360)
            ),
            style: StrokeStyle(lineWidth: 4, lineCap: .round)
          )
          .frame(width: 60, height: 60)
          .rotationEffect(.degrees(rotationAngle))
          .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: rotationAngle)

        // Center icon
        Image(systemName: "wand.and.stars")
          .font(.title2)
          .foregroundStyle(Color.brandGradient)
          .scaleEffect(scale)
          .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: scale)
      }

      VStack(spacing: 8) {
        Text(message)
          .font(.headline)
          .foregroundColor(.primary)

        if let progress = progress {
          Text("\(Int(progress * 100))%")
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }
    }
    .onAppear {
      rotationAngle = 360
      scale = 1.2
    }
  }
}

// MARK: - System Colors

private var systemBackgroundColor: Color {
  #if canImport(UIKit)
  return Color(UIColor.systemBackground)
  #else
  return Color(.windowBackgroundColor)
  #endif
}

private var systemGroupedBackgroundColor: Color {
  #if canImport(UIKit)
  return Color(UIColor.systemGroupedBackground)
  #else
  return Color(.controlBackgroundColor)
  #endif
}

// MARK: - Font Extensions

public extension Font {
  // 🌸 新拟物风格字体系统 - 圆润简洁 + 高可读性
  
  // 主要标题系统
  static let customLargeTitle = Font.system(size: 32, weight: .bold, design: .rounded)
  static let customTitle = Font.system(size: 26, weight: .bold, design: .rounded)
  static let customHeadline = Font.system(size: 20, weight: .semibold, design: .rounded)
  static let customSubheadline = Font.system(size: 17, weight: .medium, design: .rounded)
  
  // 正文系统
  static let customBody = Font.system(size: 16, weight: .regular, design: .rounded)
  static let customBodyMedium = Font.system(size: 16, weight: .medium, design: .rounded)
  static let customCallout = Font.system(size: 15, weight: .regular, design: .rounded)
  
  // 辅助文字系统
  static let customCaption = Font.system(size: 13, weight: .medium, design: .rounded)
  static let customFootnote = Font.system(size: 12, weight: .regular, design: .rounded)
  
  // 交互元素字体
  static let customButton = Font.system(size: 16, weight: .semibold, design: .rounded)
  static let customButtonLarge = Font.system(size: 18, weight: .semibold, design: .rounded)
  static let customTag = Font.system(size: 12, weight: .semibold, design: .rounded)
  
  // 特殊用途字体
  static let welcomeTitle = Font.system(size: 28, weight: .bold, design: .rounded)
  static let welcomeSubtitle = Font.system(size: 16, weight: .medium, design: .rounded)
  static let cardTitle = Font.system(size: 18, weight: .semibold, design: .rounded)
}

// MARK: - Modern Card Component (inspired by reference design)

/// A modern dark card component similar to the reference image
public struct ModernDarkCard<Content: View>: View {
  let content: Content
  let cornerRadius: CGFloat

  public init(cornerRadius: CGFloat = 24, @ViewBuilder content: () -> Content) {
    self.cornerRadius = cornerRadius
    self.content = content()
  }

  public var body: some View {
    content
      .padding(20)
      .background(
        RoundedRectangle(cornerRadius: cornerRadius)
          .fill(Color.darkCardBackground)
          .shadow(color: .black.opacity(0.15), radius: 12, x: 0, y: 4)
      )
  }
}

// MARK: - Colorful Tag Component (inspired by reference design)

/// A colorful tag component similar to the reference image
public struct ColorfulTag: View {
  let text: String
  let backgroundColor: Color
  let textColor: Color

  public init(text: String, backgroundColor: Color, textColor: Color = .black) {
    self.text = text
    self.backgroundColor = backgroundColor
    self.textColor = textColor
  }

  public var body: some View {
    Text(text)
      .font(.customTag)
      .fontWeight(.semibold)
      .foregroundColor(textColor)
      .padding(.horizontal, 12)
      .padding(.vertical, 6)
      .background(
        RoundedRectangle(cornerRadius: 12)
          .fill(backgroundColor)
      )
  }
}

// MARK: - 🍎 新拟物风格 Apple 登录按钮

/// 专为Apple登录设计的新拟物风格按钮
public struct NeumorphicAppleSignInButton: View {
  let action: () -> Void
  let isDisabled: Bool
  
  @State private var isPressed = false
  
  public init(isDisabled: Bool = false, action: @escaping () -> Void) {
    self.isDisabled = isDisabled
    self.action = action
  }
  
  public var body: some View {
    Button(action: {
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .light)
      impactFeedback.impactOccurred()
      #endif
      action()
    }) {
      HStack(spacing: 12) {
        // Apple 图标
        Image(systemName: "applelogo")
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(.textPrimary)
        
        Text("使用 Apple ID 登录")
          .font(.customButton)
          .fontWeight(.semibold)
          .foregroundColor(.textPrimary)
      }
      .frame(maxWidth: .infinity)
      .padding(.vertical, 18)
      .padding(.horizontal, 24)
      .background(
        ZStack {
          // 新拟物背景
          RoundedRectangle(cornerRadius: 20)
            .fill(
              LinearGradient(
                colors: [.white.opacity(0.95), .softBeige.opacity(0.9)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
          
          // 高光效果
          RoundedRectangle(cornerRadius: 20)
            .stroke(Color.white.opacity(0.8), lineWidth: 1)
            .blur(radius: 0.5)
            .offset(x: -1, y: -1)
          
          // 阴影效果
          RoundedRectangle(cornerRadius: 20)
            .stroke(Color.shadowInner, lineWidth: 1)
            .blur(radius: 0.5)
            .offset(x: 1, y: 1)
        }
      )
      .scaleEffect(isPressed ? 0.98 : 1.0)
      .opacity(isDisabled ? 0.5 : 1.0)
      .shadow(
        color: .shadowDark.opacity(0.15),
        radius: isPressed ? 8 : 12,
        x: 0,
        y: isPressed ? 2 : 4
      )
    }
    .disabled(isDisabled)
    .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
      withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
        isPressed = pressing
      }
    }, perform: {})
  }
}
