import SwiftUI
import ComposableArchitecture

// MARK: - 🌸 新拟物风格组件使用示例

/// 新拟物风格页面示例 - 展示如何使用设计系统
public struct NeumorphicExampleView: View {
  
  public init() {}
  
  public var body: some View {
    ScrollView {
      VStack(spacing: NeumorphicDesign.largeSpacing) {
        
        // 🌸 标题区域
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          Text("新拟物风格设计系统")
            .font(.welcomeTitle)
            .foregroundColor(.textPrimary)
          
          Text("柔和奶油色 + 粉橙渐变 + 圆润设计")
            .font(.welcomeSubtitle)
            .foregroundColor(.textSecondary)
            .multilineTextAlignment(.center)
        }
        .padding(.top, NeumorphicDesign.extraLargeSpacing)
        
        // 🌸 按钮示例
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          Text("按钮样式")
            .font(.cardTitle)
            .foregroundColor(.textPrimary)
          
          VStack(spacing: NeumorphicDesign.smallSpacing) {
            PrimaryButton(title: "主要按钮", style: .primary) {
              print("主要按钮点击")
            }
            
            PrimaryButton(title: "次要按钮", style: .secondary) {
              print("次要按钮点击")
            }
            
            PrimaryButton(title: "轮廓按钮", style: .outline) {
              print("轮廓按钮点击")
            }
            
            PrimaryButton(title: "柔和按钮", style: .soft) {
              print("柔和按钮点击")
            }
          }
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
        
        // 🌸 卡片示例
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          Text("卡片样式")
            .font(.cardTitle)
            .foregroundColor(.textPrimary)
          
          VStack(spacing: NeumorphicDesign.mediumSpacing) {
            ContentCard(style: .elevated) {
              cardContent(title: "浮起卡片", description: "具有外阴影的浮起效果")
            }
            
            ContentCard(style: .inset) {
              cardContent(title: "内嵌卡片", description: "具有内阴影的内嵌效果")
            }
            
            ContentCard(style: .flat) {
              cardContent(title: "平面卡片", description: "简洁的平面设计")
            }
            
            ContentCard(style: .soft) {
              cardContent(title: "柔和卡片", description: "轻微阴影的柔和效果")
            }
          }
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
        
        // 🌸 色彩标签示例
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          Text("色彩标签")
            .font(.cardTitle)
            .foregroundColor(.textPrimary)
          
          LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: NeumorphicDesign.smallSpacing) {
            ColorfulTag(text: "柔和粉色", backgroundColor: .softPink, textColor: .white)
            ColorfulTag(text: "温暖橘色", backgroundColor: .warmOrange, textColor: .white)
            ColorfulTag(text: "淡紫色", backgroundColor: .lightPurple, textColor: .textPrimary)
            ColorfulTag(text: "桃粉色", backgroundColor: .peachPink, textColor: .textPrimary)
            ColorfulTag(text: "成功绿", backgroundColor: .successSoft, textColor: .textPrimary)
            ColorfulTag(text: "警告黄", backgroundColor: .warningSoft, textColor: .textPrimary)
          }
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
        
        // 🌸 加载指示器示例
        VStack(spacing: NeumorphicDesign.mediumSpacing) {
          Text("加载指示器")
            .font(.cardTitle)
            .foregroundColor(.textPrimary)
          
          ContentCard(style: .soft) {
            VStack(spacing: NeumorphicDesign.mediumSpacing) {
              EnhancedLoadingView(message: "正在生成婚纱照...", progress: 0.65)
              
              Text("带进度的加载指示器")
                .font(.customCaption)
                .foregroundColor(.textSecondary)
            }
          }
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
        
        Spacer(minLength: NeumorphicDesign.extraLargeSpacing)
      }
    }
    .neumorphicBackground()
    .navigationTitle("设计系统")
    .neumorphicNavigationStyle()
  }
  
  // MARK: - Helper Views
  
  @ViewBuilder
  private func cardContent(title: String, description: String) -> some View {
    VStack(alignment: .leading, spacing: NeumorphicDesign.smallSpacing) {
      HStack {
        Image(systemName: "sparkles")
          .font(.system(size: 20))
          .foregroundStyle(Color.brandGradient)
        
        Text(title)
          .font(.customHeadline)
          .foregroundColor(.textPrimary)
        
        Spacer()
      }
      
      Text(description)
        .font(.customBody)
        .foregroundColor(.textSecondary)
        .lineLimit(2)
    }
  }
}

// MARK: - 🌸 新拟物风格表单示例

/// 展示如何创建新拟物风格的表单
public struct NeumorphicFormExample: View {
  @State private var name = ""
  @State private var email = ""
  @State private var isNotificationEnabled = false
  @State private var selectedOption = 0
  
  public init() {}
  
  public var body: some View {
    ScrollView {
      VStack(spacing: NeumorphicDesign.largeSpacing) {
        
        // 表单标题
        VStack(spacing: NeumorphicDesign.smallSpacing) {
          Text("个人信息")
            .font(.welcomeTitle)
            .foregroundColor(.textPrimary)
          
          Text("请填写您的基本信息")
            .font(.welcomeSubtitle)
            .foregroundColor(.textSecondary)
        }
        .padding(.top, NeumorphicDesign.largePadding)
        
        // 表单内容
        ContentCard(style: .soft) {
          VStack(spacing: NeumorphicDesign.largeSpacing) {
            
            // 文本输入框
            VStack(alignment: .leading, spacing: NeumorphicDesign.smallSpacing) {
              Text("姓名")
                .font(.customBodyMedium)
                .foregroundColor(.textPrimary)
              
              TextField("请输入您的姓名", text: $name)
                .font(.customBody)
                .padding(NeumorphicDesign.mediumPadding)
                .background(
                  RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
                    .fill(Color.white.opacity(0.8))
                    .shadow(color: .shadowInner, radius: 2, x: 1, y: 1)
                )
            }
            
            // 邮箱输入框
            VStack(alignment: .leading, spacing: NeumorphicDesign.smallSpacing) {
              Text("邮箱")
                .font(.customBodyMedium)
                .foregroundColor(.textPrimary)
              
              TextField("请输入您的邮箱", text: $email)
                .font(.customBody)
                #if os(iOS)
                .keyboardType(.emailAddress)
                .textInputAutocapitalization(.never)
                #endif
                .padding(NeumorphicDesign.mediumPadding)
                .background(
                  RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
                    .fill(Color.white.opacity(0.8))
                    .shadow(color: .shadowInner, radius: 2, x: 1, y: 1)
                )
            }
            
            // 开关控件
            HStack {
              Text("接收通知")
                .font(.customBodyMedium)
                .foregroundColor(.textPrimary)
              
              Spacer()
              
              Toggle("", isOn: $isNotificationEnabled)
                .toggleStyle(SwitchToggleStyle(tint: Color.softPink))
            }
            
            // 选择器
            VStack(alignment: .leading, spacing: NeumorphicDesign.smallSpacing) {
              Text("偏好设置")
                .font(.customBodyMedium)
                .foregroundColor(.textPrimary)
              
              Picker("选择偏好", selection: $selectedOption) {
                Text("选项一").tag(0)
                Text("选项二").tag(1)
                Text("选项三").tag(2)
              }
              .pickerStyle(SegmentedPickerStyle())
            }
            
            // 提交按钮
            PrimaryButton(title: "保存信息", style: .primary) {
              print("保存信息")
            }
          }
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
        
        Spacer(minLength: NeumorphicDesign.extraLargeSpacing)
      }
    }
    .neumorphicBackground()
    .navigationTitle("表单示例")
    .neumorphicNavigationStyle()
  }
}

// MARK: - Preview

#Preview("设计系统示例") {
  NavigationView {
    NeumorphicExampleView()
  }
}

#Preview("表单示例") {
  NavigationView {
    NeumorphicFormExample()
  }
}