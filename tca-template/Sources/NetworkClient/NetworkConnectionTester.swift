import Foundation
import Network

// MARK: - 网络连接测试工具

public struct NetworkConnectionTester {
  
  /// 测试到指定URL的连接
  public static func testConnection(to urlString: String) async -> Bool {
    guard let url = URL(string: urlString) else {
      print("❌ [NetworkTest] 无效的URL: \(urlString)")
      return false
    }
    
    guard let host = url.host else {
      print("❌ [NetworkTest] 无法解析主机名: \(urlString)")
      return false
    }
    
    let port = url.port ?? (url.scheme == "https" ? 443 : 80)
    
    print("🔍 [NetworkTest] 测试连接到 \(host):\(port)")
    
    // 简化实现，避免复杂的并发问题
    do {
      let url = URL(string: urlString)!
      var request = URLRequest(url: url)
      request.timeoutInterval = 3.0
      
      let (_, response) = try await URLSession.shared.data(for: request)
      
      if let httpResponse = response as? HTTPURLResponse {
        print("✅ [NetworkTest] 连接成功到 \(host):\(port)，状态码: \(httpResponse.statusCode)")
        return httpResponse.statusCode < 500
      }
      
      return true
      
    } catch {
      print("❌ [NetworkTest] 连接失败到 \(host):\(port): \(error)")
      return false
    }
  }
  
  /// 测试HTTP请求
  public static func testHTTPRequest(to urlString: String) async -> Bool {
    guard let url = URL(string: urlString) else {
      print("❌ [NetworkTest] 无效的URL: \(urlString)")
      return false
    }
    
    print("🔍 [NetworkTest] 测试HTTP请求到: \(urlString)")
    
    do {
      var request = URLRequest(url: url)
      request.httpMethod = "GET"
      request.timeoutInterval = 5.0
      
      let (_, response) = try await URLSession.shared.data(for: request)
      
      if let httpResponse = response as? HTTPURLResponse {
        print("✅ [NetworkTest] HTTP响应状态码: \(httpResponse.statusCode)")
        return httpResponse.statusCode < 500 // 接受4xx和2xx响应
      }
      
      return true
      
    } catch {
      print("❌ [NetworkTest] HTTP请求失败: \(error)")
      return false
    }
  }
  
  /// 获取本机IP地址
  public static func getLocalIPAddress() -> String? {
    var address: String?
    var ifaddr: UnsafeMutablePointer<ifaddrs>?
    
    if getifaddrs(&ifaddr) == 0 {
      var ptr = ifaddr
      while ptr != nil {
        defer { ptr = ptr?.pointee.ifa_next }
        
        let interface = ptr?.pointee
        let addrFamily = interface?.ifa_addr.pointee.sa_family
        
        if addrFamily == UInt8(AF_INET) || addrFamily == UInt8(AF_INET6) {
          let name = String(cString: (interface?.ifa_name)!)
          
          if name == "en0" || name == "en1" || name == "pdp_ip0" || name == "pdp_ip1" {
            var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
            let result = getnameinfo(interface?.ifa_addr, socklen_t((interface?.ifa_addr.pointee.sa_len)!), &hostname, socklen_t(hostname.count), nil, socklen_t(0), NI_NUMERICHOST)
            if result == 0 {
              address = String(cString: hostname)
            }
          }
        }
      }
      freeifaddrs(ifaddr)
    }
    
    return address
  }
  
  /// 完整的网络诊断
  public static func diagnoseNetworkIssue(baseURL: String) async {
    print("🔍 [NetworkTest] 开始网络诊断...")
    print("   目标URL: \(baseURL)")
    
    // 1. 检查本机IP
    if let localIP = getLocalIPAddress() {
      print("📱 [NetworkTest] 本机IP地址: \(localIP)")
    }
    
    // 2. 测试TCP连接
    let tcpResult = await testConnection(to: baseURL)
    print("🔌 [NetworkTest] TCP连接测试: \(tcpResult ? "成功" : "失败")")
    
    // 3. 测试HTTP请求
    let httpResult = await testHTTPRequest(to: baseURL)
    print("🌐 [NetworkTest] HTTP请求测试: \(httpResult ? "成功" : "失败")")
    
    // 4. 提供建议
    if !tcpResult {
      print("💡 [NetworkTest] 建议:")
      print("   1. 确保后端服务器正在运行")
      print("   2. 检查端口8000是否被占用")
      print("   3. 如果使用真实设备，请将IP改为电脑的实际IP地址")
      print("   4. 检查防火墙设置")
    }
    
    print("🔍 [NetworkTest] 网络诊断完成")
  }
}

// MARK: - C函数导入
import Darwin

private func getifaddrs(_ ifap: UnsafeMutablePointer<UnsafeMutablePointer<ifaddrs>?>) -> Int32 {
  return Darwin.getifaddrs(ifap)
}

private func freeifaddrs(_ ifa: UnsafeMutablePointer<ifaddrs>?) {
  Darwin.freeifaddrs(ifa)
}

private func getnameinfo(_ sa: UnsafePointer<sockaddr>?, _ salen: socklen_t, _ host: UnsafeMutablePointer<CChar>?, _ hostlen: socklen_t, _ serv: UnsafeMutablePointer<CChar>?, _ servlen: socklen_t, _ flags: Int32) -> Int32 {
  return Darwin.getnameinfo(sa, salen, host, hostlen, serv, servlen, flags)
}