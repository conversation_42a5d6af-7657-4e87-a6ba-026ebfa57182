import SwiftUI
import ComposableArchitecture
import CommonUI
import LaunchCore

public struct LaunchView: View {
  let store: StoreOf<Launch>
  
  public init(store: StoreOf<Launch>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      ZStack {
        // Background gradient - 使用新拟物风格背景
        LinearGradient(
          colors: [Color.softPink, Color.warmOrange.opacity(0.8)],
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        VStack(spacing: 40) {
          Spacer()
          
          // App Logo and Title
          VStack(spacing: 20) {
            // Logo placeholder
            Image(systemName: "sparkles")
              .font(.system(size: 80, weight: .light))
              .foregroundStyle(Color.white)
              .shadow(radius: 10)
            
            VStack(spacing: 8) {
              Text("蝴蝶 婚纱")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)

              Text("让您的婚纱照更加完美")
                .font(.headline)
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
            }
          }
          
          Spacer()
          
          // Loading indicator
          if store.isCheckingAuthentication {
            VStack(spacing: 16) {
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)
              
              Text("正在为您准备精彩体验...")
                .font(.body)
                .foregroundColor(.white.opacity(0.8))
            }
          }
          
          // Error message if authentication check failed
          if let error = store.authenticationError {
            VStack(spacing: 12) {
              Image(systemName: "exclamationmark.triangle.fill")
                .font(.title2)
                .foregroundColor(.yellow)
              
              Text("初始化错误")
                .font(.headline)
                .foregroundColor(.white)
              
              Text(error)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            }
          }
          
          Spacer()
        }
        .padding()
      }
      .onAppear {
        print("🎬 LaunchView: onAppear called")
        store.send(.onAppear)
      }
    }
  }
}